#!/bin/bash

# Enhanced test script for emoji overlay layout and icon visibility changes
# This script builds the debug APK, installs it, and monitors relevant logs with comprehensive testing

set -e  # Exit on any error

echo "======================================"
echo "🧪 Enhanced Emoji Overlay Test Script"
echo "======================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "${BLUE}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

print_test() {
    echo -e "${PURPLE}🧪 $1${NC}"
}

# Check if device is connected
print_step "Checking ADB connection..."
if ! adb devices | grep -q "device$"; then
    print_error "No Android device found. Please connect a device and enable USB debugging."
    exit 1
fi
print_success "Device connected"

# Get device info
DEVICE_MODEL=$(adb shell getprop ro.product.model)
ANDROID_VERSION=$(adb shell getprop ro.build.version.release)
print_step "Device: $DEVICE_MODEL (Android $ANDROID_VERSION)"

# Step 1: Clean and build debug APK
print_step "Building debug APK..."
echo "Running: ./gradlew clean assembleDebug"
if ./gradlew clean assembleDebug; then
    print_success "Build completed successfully"
else
    print_error "Build failed"
    exit 1
fi

# Step 2: Install the APK
print_step "Installing APK..."
APK_PATH="app/build/outputs/apk/debug/app-debug.apk"
if [ -f "$APK_PATH" ]; then
    echo "Installing: $APK_PATH"
    if adb install -r "$APK_PATH"; then
        print_success "Installation completed"
    else
        print_error "Installation failed"
        exit 1
    fi
else
    print_error "APK not found at $APK_PATH"
    exit 1
fi

# Step 3: Clear logcat buffer
print_step "Clearing logcat buffer..."
adb logcat -c
print_success "Logcat cleared"

# Step 4: Start the app to trigger overlay
print_step "Starting the app..."
PACKAGE_NAME="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
MAIN_ACTIVITY="com.tqhit.battery.one.activity.splash.SplashActivity"
adb shell am start -n "$PACKAGE_NAME/$MAIN_ACTIVITY"
print_success "App started"

# Wait for the app to initialize
print_info "Waiting for app initialization..."
sleep 5

# Step 5: Create log file
LOG_FILE="emoji_overlay_test_$(date +%Y%m%d_%H%M%S).txt"

print_step "Starting emoji overlay monitoring..."
echo
echo "======================================"
echo "📱 EMOJI OVERLAY LOGS & TESTING"
echo "======================================"
echo

print_info "Monitoring emoji overlay logs for 30 seconds..."
print_info "During this time, you can:"
print_warning "1. Toggle silent/vibrate mode to test ring icon"
print_warning "2. Toggle WiFi to test network icons"
print_warning "3. Check positioning and layout"
print_info "Press Ctrl+C to stop monitoring early"

# Start logcat with all relevant filters
adb logcat -s \
    "EmojiBatteryView:*" \
    "EmojiView_Rendering:*" \
    "EmojiView_Drawing:*" \
    "EmojiView_Updates:*" \
    "EmojiBatteryAccessibilityService:*" \
    "CustomStatusBarInfo:*" \
    "StatusBar_Creation:*" \
    | tee "$LOG_FILE" | while IFS= read -r line; do
    
    # Color-code different types of log messages
    if [[ $line == *"CREATING_"* ]]; then
        echo -e "${GREEN}✅ ICON CREATION: $line${NC}"
    elif [[ $line == *"ERROR"* ]]; then
        echo -e "${RED}❌ ERROR: $line${NC}"
    elif [[ $line == *"STATUS_BAR_ICONS_STATE"* ]]; then
        echo -e "${YELLOW}📊 ICON STATE: $line${NC}"
    elif [[ $line == *"LAYOUT_PARAMS"* ]]; then
        echo -e "${BLUE}📐 POSITIONING: $line${NC}"
    elif [[ $line == *"RINGER_MODE_RESULT"* ]]; then
        echo -e "${PURPLE}🔇 RINGER: $line${NC}"
    elif [[ $line == *"CELLULAR_CONNECTION_RESULT"* ]]; then
        echo -e "${CYAN}📶 CELLULAR: $line${NC}"
    elif [[ $line == *"WIFI_CONNECTION_RESULT"* ]]; then
        echo -e "${CYAN}📶 WIFI: $line${NC}"
    elif [[ $line == *"VIEW_MEASURE"* ]]; then
        echo -e "${BLUE}📏 MEASURE: $line${NC}"
    else
        echo "$line"
    fi
done &

# Store the background process PID
LOGCAT_PID=$!

# Wait for 30 seconds or until user interrupts
sleep 30

# Kill the logcat process
kill $LOGCAT_PID 2>/dev/null || true

echo
print_step "Log monitoring completed. Analyzing results..."

# Analyze the results
echo
echo "======================================"
echo "📊 TEST ANALYSIS"
echo "======================================"

if [ -f "$LOG_FILE" ]; then
    # Count different types of logs (use wc -l to avoid issues with grep -c)
    WIFI_CREATIONS=$(grep "CREATING_WIFI_ICON" "$LOG_FILE" 2>/dev/null | wc -l | tr -d ' ')
    CELLULAR_CREATIONS=$(grep "CREATING_CELLULAR_ICON" "$LOG_FILE" 2>/dev/null | wc -l | tr -d ' ')
    SILENT_CREATIONS=$(grep "CREATING_SILENT_ICON" "$LOG_FILE" 2>/dev/null | wc -l | tr -d ' ')
    POSITIONING_LOGS=$(grep "LAYOUT_PARAMS_CREATED" "$LOG_FILE" 2>/dev/null | wc -l | tr -d ' ')
    STATUS_BAR_UPDATES=$(grep "STATUS_BAR_INFO_CREATION" "$LOG_FILE" 2>/dev/null | wc -l | tr -d ' ')
    ERROR_COUNT=$(grep "ERROR" "$LOG_FILE" 2>/dev/null | wc -l | tr -d ' ')
    MEASURE_LOGS=$(grep "VIEW_MEASURE" "$LOG_FILE" 2>/dev/null | wc -l | tr -d ' ')
    
    print_info "=== LOG ANALYSIS RESULTS ==="
    echo "📊 Icon creations:"
    echo "   - WiFi icons: $WIFI_CREATIONS"
    echo "   - Cellular icons: $CELLULAR_CREATIONS"  
    echo "   - Silent/Ring icons: $SILENT_CREATIONS"
    echo
    echo "📐 Layout & positioning:"
    echo "   - Layout params created: $POSITIONING_LOGS"
    echo "   - View measurements: $MEASURE_LOGS"
    echo
    echo "📋 Status updates:"
    echo "   - Status bar updates: $STATUS_BAR_UPDATES"
    echo "   - Errors found: $ERROR_COUNT"
    
    # Check if overlay is working
    if [ "${STATUS_BAR_UPDATES:-0}" -gt 0 ]; then
        print_success "✅ Emoji overlay is functioning - status bar updates detected"
    else
        print_warning "⚠️ No status bar updates detected - overlay may not be initialized"
    fi
    
    # Check for errors
    if [ "${ERROR_COUNT:-0}" -gt 0 ]; then
        print_warning "⚠️ $ERROR_COUNT errors found in logs"
        echo "Recent errors:"
        grep "ERROR" "$LOG_FILE" | tail -3
    else
        print_success "✅ No errors found in logs"
    fi
    
    # Check specific functionality
    echo
    print_info "=== FUNCTIONALITY CHECK ==="
    
    # Check positioning
    if [ "${POSITIONING_LOGS:-0}" -gt 0 ]; then
        print_success "✅ Layout positioning working"
        grep "LAYOUT_PARAMS_CREATED" "$LOG_FILE" | head -1
    else
        print_warning "⚠️ No positioning logs found"
    fi
    
    # Check icon creation
    TOTAL_ICONS=$((${WIFI_CREATIONS:-0} + ${CELLULAR_CREATIONS:-0} + ${SILENT_CREATIONS:-0}))
    if [ "$TOTAL_ICONS" -gt 0 ]; then
        print_success "✅ Icon creation working"
    else
        print_warning "⚠️ No icon creation detected - check device state and permissions"
    fi
    
else
    print_error "❌ Log file not found: $LOG_FILE"
fi

echo
echo "======================================"
echo "📄 TEST SUMMARY"
echo "======================================"

print_success "Emoji overlay test completed!"
print_info "Log file saved: $LOG_FILE"

echo
print_info "=== NEXT STEPS ==="
echo "1. Review the log file for detailed analysis"
echo "2. Check icon visibility in the actual overlay"
echo "3. Test different device states (silent, WiFi off/on)"
echo "4. Verify positioning and alignment"

echo
print_info "=== TROUBLESHOOTING ==="
echo "- If no status updates: Check if accessibility service is enabled"
echo "- If no icons: Try changing device state (toggle silent/WiFi)"
echo "- If positioning issues: Check view padding and layout logs"

print_success "Test completed! Check $LOG_FILE for detailed logs."
echo "======================================" 