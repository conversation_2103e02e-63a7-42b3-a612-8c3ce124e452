07-03 11:42:01.715   513  2010 W ActivityTaskManager: Force removing ActivityRecord{b1e1775 u0 com.fc.p.tj.charginganimation.batterycharging.chargeeffect/com.tqhit.battery.one.features.emoji.presentation.customize.EmojiCustomizeActivity t1001 f}}: app died, no saved state
07-03 11:42:02.185   513  1725 W InputManager-JNI: Input channel object 'c5dfaf3 com.fc.p.tj.charginganimation.batterycharging.chargeeffect/com.tqhit.battery.one.features.emoji.presentation.customize.EmojiCustomizeActivity (client)' was disposed without first being removed with the input manager!
07-03 11:42:05.588 21624 21624 D BatteryApplication: STARTUP_TIMING: BatteryApplication.onCreate() started at 1751517725588
07-03 11:42:05.659 21624 21624 D BatteryApplication: STARTUP_TIMING: onCreateExt() started at 1751517725659
07-03 11:42:05.663 21624 21624 D BatteryApplication: STARTUP_TIMING: Theme initialization took 3ms
07-03 11:42:05.664 21624 21624 D BatteryApplication: STARTUP_TIMING: Language initialization took 1ms
07-03 11:42:05.664 21624 21624 D BatteryApplication: STARTUP_TIMING: onCreateExt() completed in 5ms
07-03 11:42:05.667 21624 21624 D BatteryApplication: TIMING: super.onCreate() took 79ms
07-03 11:42:05.669 21624 21624 D BatteryApplication: TIMING: Preferences operations took 0ms
07-03 11:42:05.669 21624 21624 D BatteryApplication: STARTUP_TIMING: BatteryApplication.onCreate() completed in 81ms
07-03 11:42:05.673 21624 21668 D BatteryApplication: STARTUP_TIMING: Async initialization started
07-03 11:42:05.675 21624 21668 D BatteryApplication: STARTUP_TIMING: Firebase init started at 1751517725675 on thread: DefaultDispatcher-worker-7 (ID: 73)
07-03 11:42:05.676 21624 21668 D BatteryApplication: STARTUP_TIMING: Firebase init completed in 1ms on thread: DefaultDispatcher-worker-7
07-03 11:42:05.678 21624 21668 D BatteryApplication: STARTUP_TIMING: Memory usage after Firebase init: 12MB
07-03 11:42:05.678 21624 21668 D BatteryApplication: DEPRECATED: BatteryStatusService startup disabled - using CoreBatteryStatsService instead
07-03 11:42:05.679 21624 21668 D BatteryApplication: Starting CoreBatteryStatsService from Application
07-03 11:42:05.713 21624 21668 W BatteryApplication: Missing required permissions for foreground service, starting in fallback mode
07-03 11:42:05.713 21624 21668 D CoreBatteryServiceHelper: Starting CoreBatteryStatsService
07-03 11:42:05.713 21624 21668 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:05.713 21624 21668 W CoreBatteryServiceHelper: Cannot start foreground service due to missing requirements
07-03 11:42:05.717 21624 21668 D CoreBatteryServiceHelper: CoreBatteryStatsService started as regular service (fallback mode)
07-03 11:42:05.734 21624 21667 D BatteryApplication: STARTUP_TIMING: Async remote config initialization took 62ms
07-03 11:42:05.734 21624 21668 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:05.734 21624 21668 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:05.735 21624 21668 D BatteryApplication: CoreBatteryStatsService startup status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:42:05.735 21624 21668 D BatteryApplication: Starting ChargingOverlayService from Application
07-03 11:42:05.735 21624 21668 D BatteryApplication: STARTUP_TIMING: Async battery services startup took 57ms
07-03 11:42:05.735 21624 21668 D BatteryApplication: STARTUP_TIMING: Animation preloading disabled for performance optimization
07-03 11:42:05.735 21624 21668 D BatteryApplication: STARTUP_TIMING: Thumbnail preloading disabled for performance optimization
07-03 11:42:05.735 21624 21668 D BatteryApplication: STARTUP_TIMING: Starting parallel MAX SDK initialization
07-03 11:42:05.736 21624 21668 D BatteryApplication: STARTUP_TIMING: Total async initialization took 63ms
07-03 11:42:05.736 21624 21668 D BatteryApplication: MAX_INIT: Starting AppLovin MAX SDK initialization at 1751517725736 on thread: DefaultDispatcher-worker-7 (ID: 73)
07-03 11:42:05.737 21624 21668 D BatteryApplication: MAX_INIT: Memory usage before MAX SDK init: 13MB
07-03 11:42:05.836 21624 21668 D BatteryApplication: MAX_INIT: SDK settings configured in 98ms
07-03 11:42:05.836 21624 21668 D BatteryApplication: MAX_INIT: Initialization configuration created in 0ms
07-03 11:42:05.836 21624 21668 D BatteryApplication: MAX_INIT: Starting SDK initialization call at 1751517725836
07-03 11:42:06.168 21624 21660 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:06.168 21624 21660 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:06.168 21624 21660 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:42:06.268 21624 21668 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:06.268 21624 21668 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:06.268 21624 21668 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:42:06.389 21624 21660 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:06.390 21624 21660 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:06.390 21624 21660 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:42:06.491 21624 21660 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:06.491 21624 21660 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:06.491 21624 21660 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:42:06.592 21624 21660 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:06.592 21624 21660 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:06.592 21624 21660 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:42:06.692 21624 21660 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:06.692 21624 21660 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:06.693 21624 21660 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:42:06.817 21624 21668 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:06.818 21624 21668 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:06.818 21624 21668 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:42:06.827 21624 21624 D BatteryApplication: App moved to foreground
07-03 11:42:06.827 21624 21624 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:06.870 21624 21624 D CoreBatteryStatsService: STARTUP_TIMING: CoreBatteryStatsService.onCreate() started at 1751517726870 on thread: main (ID: 2)
07-03 11:42:06.871 21624 21624 D CoreBatteryStatsService: CoreBatteryStatsService created
07-03 11:42:06.872 21624 21624 D CoreBatteryStatsService: STARTUP_TIMING: BatteryManager initialization took 1ms
07-03 11:42:06.889 21624 21624 D CoreBatteryStatsService: Unified notification channel created: unified_battery_monitor_channel
07-03 11:42:06.889 21624 21624 D CoreBatteryStatsService: STARTUP_TIMING: Notification channel creation took 17ms
07-03 11:42:06.889 21624 21624 D CoreBatteryStatsService: STARTUP_TIMING: CoreBatteryStatsService.onCreate() completed in 19ms
07-03 11:42:06.899 21624 21624 D CoreBatteryStatsService: STARTUP_TIMING: onStartCommand called at 1751517726899 on thread: main (ID: 2) with action: com.tqhit.battery.one.ACTION_START_CORE_BATTERY_MONITORING
07-03 11:42:06.899 21624 21624 D CoreBatteryStatsService: STARTUP_TIMING: Starting core battery monitoring at 1751517726899
07-03 11:42:06.899 21624 21624 D CoreBatteryStatsService: Attempting to start foreground service (Android 35, attempt 1)
07-03 11:42:06.899 21624 21624 W CoreBatteryStatsService: POST_NOTIFICATIONS permission not granted, cannot start foreground service
07-03 11:42:06.899 21624 21624 W CoreBatteryStatsService: Foreground service start failed, continuing in fallback mode
07-03 11:42:06.899 21624 21624 D CoreBatteryStatsService: Starting battery status monitoring
07-03 11:42:06.902 21624 21624 D CoreBatteryStatsService: Battery event receiver registered
07-03 11:42:06.902 21624 21624 D CoreBatteryStatsService: Getting initial battery status
07-03 11:42:06.904 21624 21624 V CoreBatteryStatsService: Extracting battery status from intent
07-03 11:42:06.906 21624 21624 D CoreBatteryStatsService: Extracted battery status: level=67/100 (67%), status=4 (charging=false), plugged=0, voltage=5000mV, temp=25.0°C, current=900000µA
07-03 11:42:06.907 21624 21624 D CoreBatteryStatsProvider: CORE_BATTERY_PROVIDER: Initial status set: CoreBatteryStatus(percentage=67, isCharging=false, pluggedSource=0, currentMicroAmperes=900000, voltageMillivolts=5000, temperatureCelsius=25.0, timestampEpochMillis=1751517726906)
07-03 11:42:06.908 21624 21624 D CoreBatteryStatus: CORE_BATTERY_STATUS_CREATED: ID=458372451, Percentage=67%, Charging=false, PluggedSource=UNPLUGGED, Current=900000µA, Voltage=5000mV, Temperature=25.0°C, Timestamp=1751517726906
07-03 11:42:06.908 21624 21624 V CoreBatteryStatsService: Skipping notification update - not running as foreground service
07-03 11:42:06.909 21624 21624 D CoreBatteryStatsService: Initial battery status emitted: CoreBatteryStatus(percentage=67, isCharging=false, pluggedSource=0, currentMicroAmperes=900000, voltageMillivolts=5000, temperatureCelsius=25.0, timestampEpochMillis=1751517726906)
07-03 11:42:06.919 21624 21660 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:06.919 21624 21660 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:06.919 21624 21660 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:42:07.020 21624 21661 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:07.020 21624 21661 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:07.021 21624 21661 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:42:07.026 21624 21624 V CoreBatteryStatsService: Battery status changed - processing intent
07-03 11:42:07.040 21624 21624 V CoreBatteryStatsService: Extracting battery status from intent
07-03 11:42:07.051 21624 21624 D CoreBatteryStatsService: Extracted battery status: level=67/100 (67%), status=4 (charging=false), plugged=0, voltage=5000mV, temp=25.0°C, current=900000µA
07-03 11:42:07.052 21624 21624 D CoreBatteryStatsService: BATTERY_UPDATE: === NEW BATTERY STATUS DETECTED ===
07-03 11:42:07.052 21624 21624 D CoreBatteryStatsService: BATTERY_UPDATE: Status details:
07-03 11:42:07.052 21624 21624 D CoreBatteryStatsService: BATTERY_UPDATE:   Percentage: 67%
07-03 11:42:07.053 21624 21624 D CoreBatteryStatsService: BATTERY_UPDATE:   Charging: false
07-03 11:42:07.053 21624 21624 D CoreBatteryStatsService: BATTERY_UPDATE:   Current: 900000µA
07-03 11:42:07.055 21624 21624 D CoreBatteryStatsService: BATTERY_UPDATE:   Temperature: 25.0°C
07-03 11:42:07.073 21624 21624 D CoreBatteryStatsService: BATTERY_UPDATE:   Timestamp: 11:42:07
07-03 11:42:07.075 21624 21624 V CoreBatteryStatsProvider: CORE_BATTERY_PROVIDER: Status updated with no significant changes
07-03 11:42:07.075 21624 21624 D CoreBatteryStatus: CORE_BATTERY_STATUS_CREATED: ID=458372370, Percentage=67%, Charging=false, PluggedSource=UNPLUGGED, Current=900000µA, Voltage=5000mV, Temperature=25.0°C, Timestamp=1751517727051
07-03 11:42:07.075 21624 21624 V CoreBatteryStatsService: Skipping notification update - not running as foreground service
07-03 11:42:07.075 21624 21624 D CoreBatteryStatsService: BATTERY_UPDATE: ✅ Status updated and emitted to all observers
07-03 11:42:07.075 21624 21624 D CoreBatteryStatsService: BATTERY_UPDATE: This will trigger health recalculation in HealthRepository
07-03 11:42:07.075 21624 21624 D CoreBatteryStatsService: Battery status updated and emitted: CoreBatteryStatus(percentage=67, isCharging=false, pluggedSource=0, currentMicroAmperes=900000, voltageMillivolts=5000, temperatureCelsius=25.0, timestampEpochMillis=1751517727051)
07-03 11:42:07.127 21624 21657 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:07.127 21624 21657 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:07.127 21624 21657 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:42:07.232 21624 21657 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:07.232 21624 21657 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:07.232 21624 21657 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:42:07.336 21624 21660 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:07.336 21624 21660 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:07.336 21624 21660 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:42:07.438 21624 21660 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:07.439 21624 21660 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:07.439 21624 21660 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:42:07.539 21624 21660 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:07.539 21624 21660 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:07.539 21624 21660 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:42:07.641 21624 21660 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:07.641 21624 21660 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:07.641 21624 21660 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:42:07.743 21624 21659 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:07.743 21624 21659 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:07.743 21624 21659 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:42:07.843 21624 21659 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:07.843 21624 21659 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:07.844 21624 21659 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:42:07.945 21624 21659 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:07.945 21624 21659 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:07.946 21624 21659 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:42:08.050 21624 21659 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:08.061 21624 21659 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:08.062 21624 21659 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:42:08.165 21624 21659 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:42:08.165 21624 21659 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:42:08.165 21624 21659 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:42:08.266 21624 21659 W ServiceInitHelper: STARTUP_TIMING: CoreBatteryStatsService not ready within timeout
07-03 11:42:08.266 21624 21659 D ServiceInitHelper: STARTUP_TIMING: Battery services initialization completed
07-03 11:42:08.367 21624 21660 D InitProgressManager: SPLASH_PROGRESS: Progress updated - 25% - Battery services ready
07-03 11:42:08.367 21624 21660 D InitProgressManager: SPLASH_PROGRESS: Battery services initialization completed successfully
07-03 11:42:08.367 21624 21660 D BatteryApplication: STARTUP_TIMING: Starting deferred MAX SDK initialization from UI
07-03 11:42:08.367 21624 21660 D BatteryApplication: MAX_INIT: Starting AppLovin MAX SDK initialization at 1751517728367 on thread: DefaultDispatcher-worker-3 (ID: 65)
07-03 11:42:08.368 21624 21660 D BatteryApplication: MAX_INIT: Memory usage before MAX SDK init: 13MB
07-03 11:42:08.368 21624 21660 D BatteryApplication: MAX_INIT: SDK settings configured in 0ms
07-03 11:42:08.368 21624 21660 D BatteryApplication: MAX_INIT: Initialization configuration created in 0ms
07-03 11:42:08.368 21624 21660 D BatteryApplication: MAX_INIT: Starting SDK initialization call at 1751517728368
07-03 11:42:08.598 21624 21624 D BatteryApplication: MAX_INIT: SDK initialization callback received after 2751ms
07-03 11:42:08.599 21624 21624 D BatteryApplication: MAX_INIT: Consent flow configured in 0ms
07-03 11:42:08.599 21624 21624 D BatteryApplication: MAX_INIT: Meta consent not available, will check after CMP flow
07-03 11:42:08.600 21624 21624 D BatteryApplication: MAX_INIT: Analytics tracker initialized in 1ms
07-03 11:42:08.600 21624 21624 D BatteryApplication: AD_ADAPTER_LOAD: Scheduling optimized parallel ad loading
07-03 11:42:08.600 21624 21624 D BatteryApplication: MAX_INIT: Ad loading scheduled in 0ms
07-03 11:42:08.600 21624 21624 D BatteryApplication: MAX_INIT: AppLovin MAX SDK initialization completed in 2864ms
07-03 11:42:08.601 21624 21624 D BatteryApplication: MAX_INIT: Memory usage after MAX SDK init: 17MB (increased by 4MB)
07-03 11:42:08.669 21624 21659 D InitProgressManager: AD_ADAPTER_LOAD: BatteryApplication instance obtained
07-03 11:42:08.777 21624 21624 D BatteryApplication: AD_ADAPTER_LOAD: Interstitial Ad loading initiated (parallel)
07-03 11:42:09.515 21624 21661 V StatsChargeRepository: STATS_CHARGE_REPO: Mapped CoreBatteryStatus to StatsChargeStatus - percentage=67%, charging=false, current=900000µA
07-03 11:42:10.176 21624 21624 D DynamicNavigationManager: Battery charging state changed: false
07-03 11:42:10.176 21624 21624 D DynamicNavigationManager: BATTERY_STATE_CHANGE: New navigation state: activeFragment=EmojiBatteryFragment, charging=false
07-03 11:42:10.177 21624 21624 I DynamicNavigationManager: DYNAMIC_SWITCHING: State transition - Unknown(null) → EmojiBatteryFragment (reason: CHARGING_STOPPED, charging: false)
07-03 11:42:10.178 21624 21624 D DynamicNavigationManager: DYNAMIC_SWITCHING: Switching from Unknown(null) to EmojiBatteryFragment
07-03 11:42:10.191 21624 21624 D DynamicNavigationManager: FRAGMENT_PERFORMANCE: Show/Hide navigation to: EmojiBatteryFragment (cached: true)
07-03 11:42:10.193 21624 21624 D DynamicNavigationManager: FRAGMENT_PERFORMANCE: Adding new fragment: EmojiBatteryFragment
07-03 11:42:10.195 21624 21624 D DynamicNavigationManager: BACK_STACK_MGMT:   📍 Target fragment: EmojiBatteryFragment
07-03 11:42:10.200 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: onCreate called - savedInstanceState: false
07-03 11:42:10.203 21624 21624 D DynamicNavigationManager: FRAGMENT_PERFORMANCE: Fragment EmojiBatteryFragment visibility after commit: false
07-03 11:42:10.205 21624 21624 D DynamicNavigationManager: SharedNavigationViewModel: Notified about fragment transition to EmojiBatteryFragment
07-03 11:42:10.206 21624 21624 D DynamicNavigationManager: FRAGMENT_CONTAINER_AUDIT: [0] EmojiBatteryFragment - visible: false, hidden: false, added: true
07-03 11:42:10.206 21624 21624 D DynamicNavigationManager: FRAGMENT_VISIBILITY: Validating visibility - Active: EmojiBatteryFragment, Visible count: 0
07-03 11:42:10.206 21624 21624 D DynamicNavigationManager: FRAGMENT_VISIBILITY: EmojiBatteryFragment - visible: false, hidden: false, added: true
07-03 11:42:10.206 21624 21624 D DynamicNavigationManager: FRAGMENT_VISIBILITY: Restored visibility for EmojiBatteryFragment
07-03 11:42:10.207 21624 21624 D DynamicNavigationManager: FRAGMENT_VISIBILITY: Visible fragments: 0, Hidden fragments: 0, Target: EmojiBatteryFragment visible=false
07-03 11:42:10.207 21624 21624 D DynamicNavigationManager: FRAGMENT_VISIBILITY: EmojiBatteryFragment - isVisible: false, isHidden: false, isAdded: true
07-03 11:42:10.210 21624 21624 D DynamicNavigationManager: DYNAMIC_SWITCHING: Navigation state updated - EmojiBatteryFragment (charging: false, reason: INITIAL_SETUP)
07-03 11:42:10.236 21624 21624 D BackgroundPermission: Battery optimization status: isIgnoring=false
07-03 11:42:10.258 21624 21624 D BackgroundPermission: Battery optimization status: isIgnoring=false
07-03 11:42:10.280 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: onCreateView called
07-03 11:42:10.401 21624 21667 D BidMachineLog: ery Charging Animation 3D","bundle":"com.fc.p.tj.charginganimation.batterycharging.chargeeffect","ver":"1.2.1.20250630","ext_proto":[{}],"release":{"type":"debug","signatureSHA1":"F4:70:DE:CB:66:7B:E6:31:03:A8:90:CF:11:9F:E5:9B:95:37:34:16","signatureSHA256":"8D:B9:D0:5F:96:48:71:2C:8B:FF:88:5A:46:09:A1:54:CC:11:8A:9F:EC:56:EE:3F:39:D3:BD:78:5B:54:DA:C3"}},"device":{"type":"DEVICE_TYPE_PHONE_DEVICE","ua":"Mozilla\/5.0 (Linux; Android 15; sdk_gphone64_arm64 Build\/AE3A.240806.036; wv) AppleWebKit\/537.36 (KHTML, like Gecko) Version\/4.0 Chrome\/137.0.7151.115 Mobile Battery Charging Animation 3D\/1.2.1.20250630","ifa":"8156452c-9f95-4837-8599-15ff7a8c8a3b","make":"Google","model":"sdk_gphone64_arm64","os":"OS_ANDROID","osv":"15","hwv":"Linux localhost 6.6.30-android15-8-gdd9c02ccfe27-ab11987101-4k #1 SMP PREEMPT Tue Jun 18 20:50:32 UTC 2024 aarch64 Toybox","h":2400,"w":1080,"ppi":420,"pxratio":2.625,"lang":"en","carrier":"T-Mobile","mccmnc":"310-260","contype":"CONNECTION_TYPE_WIFI","ge
07-03 11:42:10.439 21624 21759 I LevelPlaySDK: API: yj a - IronSourceAds.init() appkey: 21c3e341d, legacyAdFormats: [INTERSTITIAL, REWARDED, BANNER], context: BatteryApplication
07-03 11:42:10.471 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: setupUI called
07-03 11:42:10.472 21624 21624 D EmojiBatteryFragment: Banner ad setup not yet implemented
07-03 11:42:10.472 21624 21624 D EmojiBatteryFragment: UI components setup completed
07-03 11:42:10.472 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Setting up CategoryRecyclerView with 6 categories
07-03 11:42:10.472 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Initial categories: [HOT, Character, Heart, Cute, Animal, Food]
07-03 11:42:10.472 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Using remote config categories: false
07-03 11:42:10.473 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: CategoryRecyclerView setup completed with adapter containing 6 items
07-03 11:42:10.473 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Setting up battery style RecyclerView
07-03 11:42:10.474 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Layout manager set to GridLayoutManager with 3 columns
07-03 11:42:10.474 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Item decoration added with spacing: 21px
07-03 11:42:10.475 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: RecyclerView setup completed
07-03 11:42:10.475 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - Adapter: com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter@fa3292a
07-03 11:42:10.475 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - Layout Manager: androidx.recyclerview.widget.GridLayoutManager@f7f831b
07-03 11:42:10.475 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - Visibility: 0
07-03 11:42:10.475 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - Width: 0, Height: 0
07-03 11:42:10.475 21624 21624 D EmojiBatteryFragment: RecyclerViews setup completed
07-03 11:42:10.475 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Setting up ViewModel observers
07-03 11:42:10.478 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Setting up emoji categories observation
07-03 11:42:10.481 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: ViewModel observers setup completed
07-03 11:42:10.481 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Triggering LoadInitialData event
07-03 11:42:10.485 21624 21624 D BatteryGalleryVM: BatteryGalleryViewModel initialized
07-03 11:42:10.485 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Loading emoji categories
07-03 11:42:10.485 21624 21624 D BatteryGalleryVM: Handling event: LoadInitialData
07-03 11:42:10.485 21624 21624 D BatteryGalleryVM: LoadInitialData event - using emoji services
07-03 11:42:10.485 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: setupUI completed
07-03 11:42:10.486 21624 21657 D EmojiCategoryService: REMOTE_CONFIG: Fetching emoji categories from remote config
07-03 11:42:10.486 21624 21657 D EmojiCategoryService: REMOTE_CONFIG: Retrieved JSON string length: 715
07-03 11:42:10.487 21624 21657 D EmojiCategoryService: REMOTE_CONFIG: Successfully parsed 10 categories
07-03 11:42:10.487 21624 21657 D EmojiCategoryService: REMOTE_CONFIG: Filtered to 10 valid categories
07-03 11:42:10.487 21624 21657 D EmojiCategoryService: REMOTE_CONFIG: Successfully loaded 10 valid categories
07-03 11:42:10.487 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: onViewCreated called
07-03 11:42:10.491 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Starting to collect UI state from ViewModel
07-03 11:42:10.492 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: UI state collected - loading=false, styles=0
07-03 11:42:10.492 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: State details - allStyles=0, selectedCategory=HOT
07-03 11:42:10.492 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: State details - isInitialLoadComplete=false, errorMessage=null
07-03 11:42:10.492 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: updateUI called with state:
07-03 11:42:10.492 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - isLoading: false
07-03 11:42:10.492 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - displayedStyles count: 0
07-03 11:42:10.492 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - allStyles count: 0
07-03 11:42:10.493 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - selectedCategory: HOT
07-03 11:42:10.493 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - errorMessage: null
07-03 11:42:10.493 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - isInitialLoadComplete: false
07-03 11:42:10.493 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - shouldRequestPermissions: false
07-03 11:42:10.493 21624 21624 D EmojiBatteryFragment: ERROR_UI: Hiding error state
07-03 11:42:10.493 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: updateBatteryStyles called with 0 styles
07-03 11:42:10.493 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Current adapter item count: 0
07-03 11:42:10.493 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: RecyclerView adapter: com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter@fa3292a
07-03 11:42:10.493 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: RecyclerView visibility: 0
07-03 11:42:10.493 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: RecyclerView layout manager: androidx.recyclerview.widget.GridLayoutManager@f7f831b
07-03 11:42:10.493 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: updateBatteryStyles completed. Adapter now has 0 items
07-03 11:42:10.493 21624 21624 D EmojiBatteryFragment: Category selection UI state confirmed: HOT (index: 0)
07-03 11:42:10.494 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: updateUI completed successfully
07-03 11:42:10.494 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Starting to collect emoji categories from ViewModel
07-03 11:42:10.494 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: StateFlow emitted 0 categories from remote config
07-03 11:42:10.494 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Current categories in fragment: 6 (isUsingRemoteConfig: false)
07-03 11:42:10.494 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: CategoryAdapter initialized with 6 items
07-03 11:42:10.494 21624 21624 W EmojiBatteryFragment: REMOTE_CONFIG: No remote categories available, using hardcoded fallback
07-03 11:42:10.494 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Current hardcoded categories: [HOT, Character, Heart, Cute, Animal, Food]
07-03 11:42:10.513 21624 21624 D BackgroundPermission: Battery optimization status: isIgnoring=false
07-03 11:42:10.513 21624 21624 D BackgroundPermission: Battery optimization status: isIgnoring=false
07-03 11:42:10.513 21624 21624 D ServiceManager: STARTUP_TIMING: Starting UnifiedBatteryNotificationService via helper
07-03 11:42:10.513 21624 21624 D UnifiedBatteryNotificationHelper: Starting UnifiedBatteryNotificationService (Android 35)
07-03 11:42:10.516   513   713 I ActivityManager: Background started FGS: Allowed [callingPackage: com.fc.p.tj.charginganimation.batterycharging.chargeeffect; callingUid: 10212; uidState: TOP ; uidBFSL: [BFSL]; intent: Intent { cmp=com.fc.p.tj.charginganimation.batterycharging.chargeeffect/com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService }; code:PROC_STATE_TOP; tempAllowListReason:<null>; targetSdkVersion:35; callerTargetSdkVersion:35; startForegroundCount:0; bindFromPackage:null: isBindService:false]
07-03 11:42:10.527 21624 21624 D UnifiedBatteryNotificationHelper: Started UnifiedBatteryNotificationService as foreground service
07-03 11:42:10.528 21624 21624 D ServiceManager: STARTUP_TIMING: UnifiedBatteryNotificationService startup took 15ms
07-03 11:42:10.533 21624 21624 D NavigationHandler: Fragment Visibility - Active: EmojiBatteryFragment, Visible: 0, Hidden: 0
07-03 11:42:10.534 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: onResume called
07-03 11:42:10.534 21624 21624 D EmojiBatteryFragment: EMOJI_PERMISSION: Checking permissions after resume
07-03 11:42:10.534 21624 21624 D EmojiBatteryFragment: EMOJI_PERMISSION: Previous state indicates permission check needed
07-03 11:42:10.535 21624 21624 D EmojiPermission_Check: Checking all required emoji permissions
07-03 11:42:10.535 21624 21624 D EmojiPermission_Check: Checking emoji accessibility service status
07-03 11:42:10.535 21624 21624 D EmojiPermission_Check: EMOJI_ACCESSIBILITY_SERVICE_CHECK: enabled: false for service: com.fc.p.tj.charginganimation.batterycharging.chargeeffect/com.tqhit.battery.one.features.emoji.presentation.overlay.EmojiBatteryAccessibilityService
07-03 11:42:10.535 21624 21624 D EmojiPermission_Check: EMOJI_PERMISSION_CHECK: Accessibility: false, Overlay: true, All granted: false
07-03 11:42:10.535 21624 21624 D EmojiBatteryFragment: EMOJI_PERMISSION: Permissions still missing
07-03 11:42:10.535 21624 21624 D EmojiPermission_Check: Getting emoji permission status summary
07-03 11:42:10.535 21624 21624 D EmojiPermission_Check: Checking all required emoji permissions
07-03 11:42:10.535 21624 21624 D EmojiPermission_Check: Checking emoji accessibility service status
07-03 11:42:10.536 21624 21624 D EmojiPermission_Check: EMOJI_ACCESSIBILITY_SERVICE_CHECK: enabled: false for service: com.fc.p.tj.charginganimation.batterycharging.chargeeffect/com.tqhit.battery.one.features.emoji.presentation.overlay.EmojiBatteryAccessibilityService
07-03 11:42:10.536 21624 21624 D EmojiPermission_Check: EMOJI_PERMISSION_CHECK: Accessibility: false, Overlay: true, All granted: false
07-03 11:42:10.536 21624 21624 D EmojiPermission_Check: Checking emoji accessibility service status
07-03 11:42:10.536 21624 21624 D EmojiPermission_Check: EMOJI_ACCESSIBILITY_SERVICE_CHECK: enabled: false for service: com.fc.p.tj.charginganimation.batterycharging.chargeeffect/com.tqhit.battery.one.features.emoji.presentation.overlay.EmojiBatteryAccessibilityService
07-03 11:42:10.538 21624 21624 D EmojiPermission_Check: EMOJI_PERMISSION_STATUS: {hasAllRequiredPermissions=false, isAccessibilityServiceEnabled=false, isOverlayPermissionGranted=false, androidApiLevel=35, needsOverlayPermission=false, serviceName=com.fc.p.tj.charginganimation.batterycharging.chargeeffect/com.tqhit.battery.one.features.emoji.presentation.overlay.EmojiBatteryAccessibilityService}
07-03 11:42:10.538 21624 21624 D EmojiBatteryFragment: EMOJI_PERMISSION: Current permission status: {hasAllRequiredPermissions=false, isAccessibilityServiceEnabled=false, isOverlayPermissionGranted=false, androidApiLevel=35, needsOverlayPermission=false, serviceName=com.fc.p.tj.charginganimation.batterycharging.chargeeffect/com.tqhit.battery.one.features.emoji.presentation.overlay.EmojiBatteryAccessibilityService}
07-03 11:42:10.538 21624 21624 D BatteryGalleryVM: Handling event: OnResume
07-03 11:42:10.538 21624 21624 D BatteryGalleryVM: Handling system event: OnResume
07-03 11:42:10.713 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: About to emit 10 categories to StateFlow
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: StateFlow emitted 10 categories from remote config
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Current categories in fragment: 6 (isUsingRemoteConfig: false)
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: CategoryAdapter initialized with 6 items
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Processing remote categories for UI update
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Starting category tabs update with 10 remote categories
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Current fragment state - categories: 6, selectedIndex: 0
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Mapped 🔥 HOT -> HOT, isNew=false
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Mapped Brainrot -> Brainrot, isNew=true
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Mapped Character  -> Character, isNew=false
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Mapped Heart -> Heart, isNew=false
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Mapped Cute -> Cute, isNew=false
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Mapped Sticker 3D -> Sticker 3D, isNew=true
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Mapped Emotion -> Emotion, isNew=false
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Mapped Animal -> Animal, isNew=false
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Mapped Food -> Food, isNew=false
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Mapped Other -> Other, isNew=false
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Successfully mapped 10 compatible categories
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Category 0: HOT (isNew=false)
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Category 1: Brainrot (isNew=true)
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Category 2: Character (isNew=false)
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Category 3: Heart (isNew=false)
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Category 4: Cute (isNew=false)
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Category 5: Sticker 3D (isNew=true)
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Category 6: Emotion (isNew=false)
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Category 7: Animal (isNew=false)
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Category 8: Food (isNew=false)
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Category 9: Other (isNew=false)
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Updated fragment categories from 6 to 10
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Old categories: [HOT, Character, Heart, Cute, Animal, Food]
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: New categories: [HOT, Brainrot, Character, Heart, Cute, Sticker 3D, Emotion, Animal, Food, Other]
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Selection update - old index: 0, new index: 0
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Updating CategoryAdapter with 10 categories using DiffUtil
07-03 11:42:10.714 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Selected index: 0
07-03 11:42:10.717 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Updating ViewModel with selected category: HOT
07-03 11:42:10.717 21624 21624 D BatteryGalleryVM: Handling event: SelectCategory
07-03 11:42:10.717 21624 21624 D BatteryGalleryVM: Selecting category: HOT
07-03 11:42:10.718 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Mapped category HOT to categoryId: hot_category
07-03 11:42:10.719 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Loading emoji items for category: hot_category
07-03 11:42:10.720 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: UI state collected - loading=true, styles=0
07-03 11:42:10.722 21624 21661 D EmojiItemService: REMOTE_CONFIG: Fetching emoji items for category: hot_category
07-03 11:42:10.722 21624 21661 D EmojiItemService: REMOTE_CONFIG: Retrieved JSON string length: 62639 for category: hot_category
07-03 11:42:10.723 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: State details - allStyles=0, selectedCategory=HOT
07-03 11:42:10.724 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: State details - isInitialLoadComplete=false, errorMessage=null
07-03 11:42:10.724 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: updateUI called with state:
07-03 11:42:10.724 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - isLoading: true
07-03 11:42:10.724 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - displayedStyles count: 0
07-03 11:42:10.724 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - allStyles count: 0
07-03 11:42:10.724 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - selectedCategory: HOT
07-03 11:42:10.724 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - errorMessage: null
07-03 11:42:10.724 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - isInitialLoadComplete: false
07-03 11:42:10.724 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - shouldRequestPermissions: false
07-03 11:42:10.724 21624 21624 D EmojiBatteryFragment: ERROR_UI: Hiding error state
07-03 11:42:10.724 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: updateBatteryStyles called with 0 styles
07-03 11:42:10.724 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Current adapter item count: 0
07-03 11:42:10.724 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: RecyclerView adapter: com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter@fa3292a
07-03 11:42:10.724 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: RecyclerView visibility: 0
07-03 11:42:10.724 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: RecyclerView layout manager: androidx.recyclerview.widget.GridLayoutManager@f7f831b
07-03 11:42:10.724 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: updateBatteryStyles completed. Adapter now has 0 items
07-03 11:42:10.727 21624 21624 D EmojiBatteryFragment: Category selection UI state confirmed: HOT (index: 0)
07-03 11:42:10.727 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: updateUI completed successfully
07-03 11:42:10.727 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Successfully emitted 10 emoji categories to StateFlow
07-03 11:42:10.727 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: StateFlow current value has 10 categories
07-03 11:42:10.727 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Category 0: 🔥 HOT (id=hot_category, priority=1, isNew=false)
07-03 11:42:10.727 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Category 1: Brainrot (id=brainrot_category, priority=2, isNew=true)
07-03 11:42:10.727 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Category 2: Character  (id=character_category, priority=3, isNew=false)
07-03 11:42:10.727 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Category 3: Heart (id=heart_category, priority=4, isNew=false)
07-03 11:42:10.727 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Category 4: Cute (id=cute_category, priority=5, isNew=false)
07-03 11:42:10.727 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Category 5: Sticker 3D (id=sticker3d_category, priority=6, isNew=true)
07-03 11:42:10.727 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Category 6: Emotion (id=emotion_category, priority=7, isNew=false)
07-03 11:42:10.727 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Category 7: Animal (id=animal_category, priority=8, isNew=false)
07-03 11:42:10.727 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Category 8: Food (id=food_category, priority=9, isNew=false)
07-03 11:42:10.727 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Category 9: Other (id=other_category, priority=10, isNew=false)
07-03 11:42:10.751 21624 21624 D UnifiedBatteryService_Lifecycle: Unified battery notification service created (Android 35)
07-03 11:42:10.753 21624 21624 D UnifiedBatteryService_Lifecycle: Service state: foreground=false, fallback=false
07-03 11:42:10.756 21624 21624 D UnifiedBatteryNotificationService: Unified battery notification channel created: unified_battery_monitor_channel
07-03 11:42:10.759 21624 21624 D UnifiedBatteryNotificationService: Screen state receiver registered
07-03 11:42:10.759 21624 21624 D UnifiedBatteryService_Lifecycle: Service onCreate completed successfully
07-03 11:42:10.761 21624 21624 D UnifiedBatteryService_Lifecycle: Service start command received - flags: 0, startId: 1
07-03 11:42:10.761 21624 21624 D UnifiedBatteryService_Lifecycle: Current monitoring state: false, foreground: false, fallback: false
07-03 11:42:10.761 21624 21624 D UnifiedBatteryService_Lifecycle: Starting monitoring for the first time
07-03 11:42:10.761 21624 21624 D UnifiedBatteryService_Monitoring: Starting unified battery monitoring with CoreBatteryStatsService (Android 35)
07-03 11:42:10.761 21624 21624 D UnifiedBatteryService_Monitoring: Initial state: foreground=false, fallback=false
07-03 11:42:10.761 21624 21624 D UnifiedBatteryNotificationService: Starting CoreBatteryStatsService for unified notifications
07-03 11:42:10.767 21624 21624 D UnifiedBatteryService_Foreground: Attempting to start foreground service (Android 35)
07-03 11:42:10.778 21624 21624 D UnifiedBatteryService_Foreground: Successfully started as foreground service
07-03 11:42:10.779 21624 21624 D UnifiedBatteryService_Monitoring: Monitoring startup completed - foreground=true, fallback=false
07-03 11:42:10.781 21624 21659 D UnifiedBatteryService_Monitoring: Starting CoreBatteryStatus flow collection
07-03 11:42:10.782 21624 21659 D UnifiedBatteryService_Monitoring: New battery status: 67%, charging=false, current=900mA, voltage=5V
07-03 11:42:10.799 21624 21661 D EmojiItemService: REMOTE_CONFIG: Successfully parsed 115 emoji items for category: hot_category
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Filtered to 115 valid items for category: hot_category
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 0 in hot_category: Emoji Battery Cartoon_04 (id=emoji-1, priority=1, isPremium=true)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 1 in hot_category: Emoji Battery Cartoon_01 (id=emoji-2, priority=2, isPremium=true)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 2 in hot_category: Emoji Battery Brainrot_01 (id=emoji-3, priority=3, isPremium=true)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 3 in hot_category: Emoji Battery Sticker3D_02 (id=emoji-4, priority=4, isPremium=false)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 4 in hot_category: Emoji Battery Cartoon_13 (id=emoji-5, priority=5, isPremium=true)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 5 in hot_category: Emoji Battery Brainrot_03 (id=emoji-6, priority=6, isPremium=true)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 6 in hot_category: Emoji Battery Sticker3D_05 (id=emoji-7, priority=7, isPremium=true)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 7 in hot_category: battery_cartoon_20 (id=emoji-8, priority=8, isPremium=false)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 8 in hot_category: battery_cartoon_16 (id=emoji-9, priority=9, isPremium=true)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 9 in hot_category: battery_cartoon_15 (id=emoji-10, priority=10, isPremium=false)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 10 in hot_category: Emoji Battery Sticker3D_20 (id=emoji-11, priority=11, isPremium=false)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 11 in hot_category: Emoji Battery Brainrot_04 (id=emoji-12, priority=12, isPremium=true)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 12 in hot_category: Emoji Battery Brainrot_08 (id=emoji-13, priority=13, isPremium=false)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 13 in hot_category: Emoji Battery Brainrot_06 (id=emoji-14, priority=14, isPremium=false)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 14 in hot_category: Emoji Battery Sticker3D_07 (id=emoji-15, priority=15, isPremium=false)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 15 in hot_category: emoji_battery_heart_04 (id=emoji-16, priority=16, isPremium=true)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 16 in hot_category: battery_cartoon_18 (id=emoji-17, priority=17, isPremium=false)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 17 in hot_category: Emoji Battery Cartoon_03 (id=emoji-18, priority=18, isPremium=false)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 18 in hot_category: Emoji Battery Brainrot_05 (id=emoji-19, priority=19, isPremium=false)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 19 in hot_category: Emoji Battery Cartoon_05 (id=emoji-20, priority=20, isPremium=true)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 20 in hot_category: EmojiBattery_Heart_12 (id=emoji-21, priority=21, isPremium=true)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 21 in hot_category: Emoji Battery Brainrot_11 (id=emoji-22, priority=22, isPremium=true)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 22 in hot_category: emoji_battery_heart_01 (id=emoji-23, priority=23, isPremium=true)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 23 in hot_category: Emoji Battery Cartoon_07 (id=emoji-24, priority=24, isPremium=false)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 24 in hot_category: emoji_battery_heart_10 (id=emoji-25, priority=25, isPremium=false)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 25 in hot_category: EmojiBattery_Heart_04 (id=emoji-26, priority=26, isPremium=true)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 26 in hot_category: battery_cartoon_03 (id=emoji-27, priority=27, isPremium=true)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 27 in hot_category: EmojiBattery (id=emoji-28, priority=28, isPremium=false)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 28 in hot_category: emoji_battery_animal_10 (id=emoji-29, priority=29, isPremium=false)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 29 in hot_category: emoji battery_01-1 (id=emoji-30, priority=30, isPremium=false)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 30 in hot_category: Emoji Battery_3 (id=emoji-31, priority=31, isPremium=false)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 31 in hot_category: battery_cartoon_02 (id=emoji-32, priority=32, isPremium=true)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 32 in hot_category: EmojiBattery_Cute_04 (id=emoji-33, priority=33, isPremium=false)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 33 in hot_category: Emoji Battery Cartoon_10 (id=emoji-34, priority=34, isPremium=true)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 34 in hot_category: emoji_battery_food_01 (id=emoji-35, priority=35, isPremium=false)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 35 in hot_category: emoji battery_10 (id=emoji-36, priority=36, isPremium=false)
07-03 11:42:10.801 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 36 in hot_category: emoji_battery_cute_05 (id=emoji-37, priority=37, isPremium=true)
07-03 11:42:10.802 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 37 in hot_category: emoji battery food_12 (id=emoji-38, priority=38, isPremium=false)
07-03 11:42:10.802 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 38 in hot_category: Emoji Battery_13 (id=emoji-39, priority=39, isPremium=false)
07-03 11:42:10.802 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 39 in hot_category: emoji_battery_animal_09 (id=emoji-40, priority=40, isPremium=true)
07-03 11:42:10.802 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 40 in hot_category: EmojiBattery_Cute_10 (id=emoji-41, priority=41, isPremium=false)
07-03 11:42:10.802 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 41 in hot_category: battery_cartoon_03 (id=emoji-42, priority=42, isPremium=true)
07-03 11:42:10.802 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 42 in hot_category: Emoji Battery_1 (id=emoji-43, priority=43, isPremium=false)
07-03 11:42:10.802 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 43 in hot_category: EmojiBattery_Heart_07 (id=emoji-44, priority=44, isPremium=true)
07-03 11:42:10.802 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 44 in hot_category: emoji battery_20 (id=emoji-45, priority=45, isPremium=false)
07-03 11:42:10.802 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 45 in hot_category: EmojiBattery_Heart_13 (id=emoji-46, priority=46, isPremium=true)
07-03 11:42:10.802 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 46 in hot_category: emoji battery_16 (id=emoji-47, priority=47, isPremium=false)
07-03 11:42:10.802 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 47 in hot_category: emoji battery_15 (id=emoji-48, priority=48, isPremium=false)
07-03 11:42:10.802 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 48 in hot_category: Emoji Battery Cartoon_14 (id=emoji-49, priority=49, isPremium=true)
07-03 11:42:10.802 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 49 in hot_category: Emoji Battery Emotion_05 (id=emoji-50, priority=50, isPremium=true)
07-03 11:42:10.802 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 50 in hot_category: Emoji Battery Cartoon_12 (id=emoji-51, priority=51, isPremium=true)
07-03 11:42:10.802 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 51 in hot_category: EmojiBattery_Heart_06 (id=emoji-52, priority=52, isPremium=false)
07-03 11:42:10.802 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 52 in hot_category: battery_cartoon_06 (id=emoji-53, priority=53, isPremium=false)
07-03 11:42:10.802 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 53 in hot_category: Emoji Battery_9 (id=emoji-54, priority=54, isPremium=false)
07-03 11:42:10.802 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 54 in hot_category: Emoji Battery Emotion_11 (id=emoji-55, priority=55, isPremium=false)
07-03 11:42:10.802 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 55 in hot_category: battery_cartoon_07 (id=emoji-56, priority=56, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 56 in hot_category: emoji battery food_14 (id=emoji-57, priority=57, isPremium=true)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 57 in hot_category: emoji battery_01 (id=emoji-58, priority=58, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 58 in hot_category: EmojiBattery_Heart_17 (id=emoji-59, priority=59, isPremium=true)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 59 in hot_category: Emoji Battery Sticker3D_13 (id=emoji-60, priority=60, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 60 in hot_category: battery_cartoon_01 (id=emoji-61, priority=61, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 61 in hot_category: EmojiBattery_Heart_08 (id=emoji-62, priority=62, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 62 in hot_category: battery_cartoon_09 (id=emoji-63, priority=63, isPremium=true)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 63 in hot_category: Emoji Battery Brainrot_14 (id=emoji-64, priority=64, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 64 in hot_category: emoji_battery_food_04 (id=emoji-65, priority=65, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 65 in hot_category: emoji battery_09 (id=emoji-66, priority=66, isPremium=true)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 66 in hot_category: Emoji Battery_15 (id=emoji-67, priority=67, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 67 in hot_category: Emoji Battery Brainrot_15 (id=emoji-68, priority=68, isPremium=true)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 68 in hot_category: emoji battery food_01 (id=emoji-69, priority=69, isPremium=true)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 69 in hot_category: emoji battery_07 (id=emoji-70, priority=70, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 70 in hot_category: battery_cartoon_13 (id=emoji-71, priority=71, isPremium=true)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 71 in hot_category: Emoji Battery Cartoon_02 (id=emoji-72, priority=72, isPremium=true)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 72 in hot_category: battery_cartoon_12 (id=emoji-73, priority=73, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 73 in hot_category: Emoji Battery Emotion_01 (id=emoji-74, priority=74, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 74 in hot_category: EmojiBattery_Heart_09 (id=emoji-75, priority=75, isPremium=true)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 75 in hot_category: emoji_battery_heart_07 (id=emoji-76, priority=76, isPremium=true)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 76 in hot_category: Emoji Battery_7 (id=emoji-77, priority=77, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 77 in hot_category: emoji_battery_animal_03 (id=emoji-78, priority=78, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 78 in hot_category: emoji battery food_02 (id=emoji-79, priority=79, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 79 in hot_category: battery_cartoon_14 (id=emoji-80, priority=80, isPremium=true)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 80 in hot_category: emoji_battery_food_07 (id=emoji-81, priority=81, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 81 in hot_category: emoji_battery_animal_02 (id=emoji-82, priority=82, isPremium=true)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 82 in hot_category: Emoji Battery_6 (id=emoji-83, priority=83, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 83 in hot_category: Emoji Battery Sticker3D_18 (id=emoji-84, priority=84, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 84 in hot_category: EmojiBattery_Heart_05 (id=emoji-85, priority=85, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 85 in hot_category: Emoji Battery Brainrot_19 (id=emoji-86, priority=86, isPremium=true)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 86 in hot_category: Emoji Battery_17 (id=emoji-87, priority=87, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 87 in hot_category: battery_cartoon_09 (id=emoji-88, priority=88, isPremium=true)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 88 in hot_category: EmojiBattery_Cute_01 (id=emoji-89, priority=89, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 89 in hot_category: emoji_battery_cute_07 (id=emoji-90, priority=90, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 90 in hot_category: EmojiBattery_Heart_10 (id=emoji-91, priority=91, isPremium=true)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 91 in hot_category: battery_cartoon_18 (id=emoji-92, priority=92, isPremium=true)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 92 in hot_category: emoji battery food_06 (id=emoji-93, priority=93, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 93 in hot_category: emoji_battery_food_03 (id=emoji-94, priority=94, isPremium=false)
07-03 11:42:10.803 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 94 in hot_category: Emoji Battery Brainrot_20 (id=emoji-95, priority=95, isPremium=false)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 95 in hot_category: battery_cartoon_07 (id=emoji-96, priority=96, isPremium=false)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 96 in hot_category: battery_cartoon_20 (id=emoji-97, priority=97, isPremium=true)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 97 in hot_category: emoji battery_04 (id=emoji-98, priority=98, isPremium=true)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 98 in hot_category: emoji battery_01 (id=emoji-99, priority=99, isPremium=false)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 99 in hot_category: Emoji Battery_18 (id=emoji-100, priority=100, isPremium=false)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 100 in hot_category: battery_cartoon_10 (id=emoji-101, priority=101, isPremium=false)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 101 in hot_category: battery_cartoon_11 (id=emoji-102, priority=102, isPremium=true)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 102 in hot_category: Emoji Battery Emotion_08 (id=emoji-103, priority=103, isPremium=false)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 103 in hot_category: Emoji Battery Emotion_07 (id=emoji-104, priority=104, isPremium=false)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 104 in hot_category: emoji_battery_animal_07 (id=emoji-105, priority=105, isPremium=false)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 105 in hot_category: emoji battery food_08 (id=emoji-106, priority=106, isPremium=false)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 106 in hot_category: Emoji Battery Emotion_02 (id=emoji-107, priority=107, isPremium=false)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 107 in hot_category: emoji_battery_cute_03 (id=emoji-108, priority=108, isPremium=true)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 108 in hot_category: emoji battery_17 (id=emoji-109, priority=109, isPremium=false)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 109 in hot_category: emoji_battery_cute_06 (id=emoji-110, priority=110, isPremium=false)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 110 in hot_category: emoji_battery_heart_08 (id=emoji-111, priority=111, isPremium=true)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 111 in hot_category: Emoji Battery Emotion_20 (id=emoji-112, priority=112, isPremium=false)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 112 in hot_category: Emoji Battery Emotion_17 (id=emoji-113, priority=113, isPremium=false)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 113 in hot_category: emoji battery_03 (id=emoji-114, priority=114, isPremium=false)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Item 114 in hot_category: Emoji Battery_28 (id=emoji-115, priority=115, isPremium=false)
07-03 11:42:10.804 21624 21661 D EmojiItemService: REMOTE_CONFIG: Successfully loaded 115 valid items for category: hot_category
07-03 11:42:11.635 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: DiffUtil update completed for 10 categories
07-03 11:42:11.635 21624 21624 D EmojiBatteryFragment: REMOTE_CONFIG: Scrolled to selected category at index 0
07-03 11:42:11.635 21624 21624 D CoreBatteryStatsService: STARTUP_TIMING: onStartCommand called at 1751517731635 on thread: main (ID: 2) with action: com.tqhit.battery.one.ACTION_START_CORE_BATTERY_MONITORING
07-03 11:42:11.635 21624 21624 D CoreBatteryStatsService: STARTUP_TIMING: Starting core battery monitoring at 1751517731635
07-03 11:42:11.635 21624 21624 D CoreBatteryStatsService: Attempting to start foreground service (Android 35, attempt 1)
07-03 11:42:11.638 21624 21624 W CoreBatteryStatsService: POST_NOTIFICATIONS permission not granted, cannot start foreground service
07-03 11:42:11.638 21624 21624 W CoreBatteryStatsService: Foreground service start failed, continuing in fallback mode
07-03 11:42:11.638 21624 21624 W CoreBatteryStatsService: Battery monitoring already started
07-03 11:42:11.641 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Loaded 115 emoji items for category: hot_category
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Converted to 115 battery styles
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: updateStateWithEmojiItems called with 115 emoji styles
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 0: name='Emoji Battery Cartoon_04', id='emoji-1', premium=true
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 0 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739327485_EmojiBatteryCartoon_04.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739327485_EmojiBatteryCartoon_04.png'
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 1: name='Emoji Battery Cartoon_01', id='emoji-2', premium=true
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 1 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739327485_EmojiBatteryCartoon_01.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739327485_EmojiBatteryCartoon_01.png'
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 2: name='Emoji Battery Brainrot_01', id='emoji-3', premium=true
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 2 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_01.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_01.png'
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 3: name='Emoji Battery Sticker3D_02', id='emoji-4', premium=false
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 3 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-03-03/1740989496_EmojiBatterySticker3D_02.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-03-03/1740989496_EmojiBatterySticker3D_02.png'
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 4: name='Emoji Battery Cartoon_13', id='emoji-5', premium=true
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 4 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739327486_EmojiBatteryCartoon_13.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739327486_EmojiBatteryCartoon_13.png'
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 5: name='Emoji Battery Brainrot_03', id='emoji-6', premium=true
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 5 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_03.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_03.png'
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 6: name='Emoji Battery Sticker3D_05', id='emoji-7', premium=true
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 6 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-03-03/1740989496_EmojiBatterySticker3D_05.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-03-03/1740989496_EmojiBatterySticker3D_05.png'
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 7: name='battery_cartoon_20', id='emoji-8', premium=false
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 7 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_20.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_20.png'
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 8: name='battery_cartoon_16', id='emoji-9', premium=true
07-03 11:42:11.642 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 8 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_16.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_16.png'
07-03 11:42:11.643 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 9: name='battery_cartoon_15', id='emoji-10', premium=false
07-03 11:42:11.643 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 9 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_15.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_15.png'
07-03 11:42:11.643 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 10: name='Emoji Battery Sticker3D_20', id='emoji-11', premium=false
07-03 11:42:11.643 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 10 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-03-03/1740989496_EmojiBatterySticker3D_20.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-03-03/1740989496_EmojiBatterySticker3D_20.png'
07-03 11:42:11.643 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 11: name='Emoji Battery Brainrot_04', id='emoji-12', premium=true
07-03 11:42:11.643 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 11 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_04.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_04.png'
07-03 11:42:11.643 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 12: name='Emoji Battery Brainrot_08', id='emoji-13', premium=false
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 12 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_08.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_08.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 13: name='Emoji Battery Brainrot_06', id='emoji-14', premium=false
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 13 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_06.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_06.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 14: name='Emoji Battery Sticker3D_07', id='emoji-15', premium=false
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 14 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-03-03/1740989496_EmojiBatterySticker3D_07.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-03-03/1740989496_EmojiBatterySticker3D_07.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 15: name='emoji_battery_heart_04', id='emoji-16', premium=true
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 15 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761610_emoji_battery_heart_04.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761610_emoji_battery_heart_04.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 16: name='battery_cartoon_18', id='emoji-17', premium=false
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 16 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_18.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_18.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 17: name='Emoji Battery Cartoon_03', id='emoji-18', premium=false
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 17 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739328230_EmojiBatteryCartoon_03.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739328230_EmojiBatteryCartoon_03.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 18: name='Emoji Battery Brainrot_05', id='emoji-19', premium=false
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 18 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_05.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_05.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 19: name='Emoji Battery Cartoon_05', id='emoji-20', premium=true
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 19 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739328231_EmojiBatteryCartoon_05.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739328231_EmojiBatteryCartoon_05.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 20: name='EmojiBattery_Heart_12', id='emoji-21', premium=true
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 20 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736246030_EmojiBattery_Heart_12.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736246030_EmojiBattery_Heart_12.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 21: name='Emoji Battery Brainrot_11', id='emoji-22', premium=true
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 21 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_11.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_11.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 22: name='emoji_battery_heart_01', id='emoji-23', premium=true
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 22 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761609_emoji_battery_heart_01.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761609_emoji_battery_heart_01.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 23: name='Emoji Battery Cartoon_07', id='emoji-24', premium=false
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 23 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739327485_EmojiBatteryCartoon_07.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739327485_EmojiBatteryCartoon_07.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 24: name='emoji_battery_heart_10', id='emoji-25', premium=false
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 24 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761612_emoji_battery_heart_10.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761612_emoji_battery_heart_10.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 25: name='EmojiBattery_Heart_04', id='emoji-26', premium=true
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 25 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736246029_EmojiBattery_Heart_04.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736246029_EmojiBattery_Heart_04.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 26: name='battery_cartoon_03', id='emoji-27', premium=true
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 26 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_03.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_03.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 27: name='EmojiBattery', id='emoji-28', premium=false
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 27 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-08/1736323792_emojibattery_04.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-08/1736323792_emojibattery_04.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 28: name='emoji_battery_animal_10', id='emoji-29', premium=false
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 28 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761939_emoji_battery_animal_10.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761939_emoji_battery_animal_10.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 29: name='emoji battery_01-1', id='emoji-30', premium=false
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 29 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-08/1736322236_emojibattery_01-1.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-08/1736322236_emojibattery_01-1.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 30: name='Emoji Battery_3', id='emoji-31', premium=false
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 30 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840514_EmojiBattery_3.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840514_EmojiBattery_3.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 31: name='battery_cartoon_02', id='emoji-32', premium=true
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 31 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746603941_battery_cartoon_02.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746603941_battery_cartoon_02.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 32: name='EmojiBattery_Cute_04', id='emoji-33', premium=false
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 32 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736246029_EmojiBattery_Cute_04.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736246029_EmojiBattery_Cute_04.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 33: name='Emoji Battery Cartoon_10', id='emoji-34', premium=true
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 33 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739327486_EmojiBatteryCartoon_10.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739327486_EmojiBatteryCartoon_10.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 34: name='emoji_battery_food_01', id='emoji-35', premium=false
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 34 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823656_emoji_battery_food_01.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823656_emoji_battery_food_01.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 35: name='emoji battery_10', id='emoji-36', premium=false
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 35 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245743_emojibattery_10.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245743_emojibattery_10.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 36: name='emoji_battery_cute_05', id='emoji-37', premium=true
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 36 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761119_emoji_battery_cute_05.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761119_emoji_battery_cute_05.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 37: name='emoji battery food_12', id='emoji-38', premium=false
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 37 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823383_emojibatteryfood_12.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823383_emojibatteryfood_12.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 38: name='Emoji Battery_13', id='emoji-39', premium=false
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 38 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840515_EmojiBattery_13.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840515_EmojiBattery_13.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 39: name='emoji_battery_animal_09', id='emoji-40', premium=true
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 39 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761939_emoji_battery_animal_09.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761939_emoji_battery_animal_09.png'
07-03 11:42:11.645 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 40: name='EmojiBattery_Cute_10', id='emoji-41', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 40 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736246029_EmojiBattery_Cute_10.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736246029_EmojiBattery_Cute_10.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 41: name='battery_cartoon_03', id='emoji-42', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 41 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746603941_battery_cartoon_03.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746603941_battery_cartoon_03.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 42: name='Emoji Battery_1', id='emoji-43', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 42 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840513_EmojiBattery_1.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840513_EmojiBattery_1.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 43: name='EmojiBattery_Heart_07', id='emoji-44', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 43 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736246030_EmojiBattery_Heart_07.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736246030_EmojiBattery_Heart_07.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 44: name='emoji battery_20', id='emoji-45', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 44 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245743_emojibattery_20.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245743_emojibattery_20.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 45: name='EmojiBattery_Heart_13', id='emoji-46', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 45 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736246030_EmojiBattery_Heart_13.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736246030_EmojiBattery_Heart_13.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 46: name='emoji battery_16', id='emoji-47', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 46 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245743_emojibattery_16.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245743_emojibattery_16.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 47: name='emoji battery_15', id='emoji-48', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 47 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245743_emojibattery_15.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245743_emojibattery_15.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 48: name='Emoji Battery Cartoon_14', id='emoji-49', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 48 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739327486_EmojiBatteryCartoon_14.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739327486_EmojiBatteryCartoon_14.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 49: name='Emoji Battery Emotion_05', id='emoji-50', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 49 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823857_EmojiBatteryEmotion_05.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823857_EmojiBatteryEmotion_05.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 50: name='Emoji Battery Cartoon_12', id='emoji-51', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 50 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739327486_EmojiBatteryCartoon_12.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739327486_EmojiBatteryCartoon_12.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 51: name='EmojiBattery_Heart_06', id='emoji-52', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 51 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-08/1736308486_EmojiBattery_Heart_06.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-08/1736308486_EmojiBattery_Heart_06.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 52: name='battery_cartoon_06', id='emoji-53', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 52 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746603941_battery_cartoon_06.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746603941_battery_cartoon_06.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 53: name='Emoji Battery_9', id='emoji-54', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 53 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840515_EmojiBattery_9.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840515_EmojiBattery_9.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 54: name='Emoji Battery Emotion_11', id='emoji-55', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 54 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823858_EmojiBatteryEmotion_11.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823858_EmojiBatteryEmotion_11.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 55: name='battery_cartoon_07', id='emoji-56', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 55 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746603942_battery_cartoon_07.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746603942_battery_cartoon_07.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 56: name='emoji battery food_14', id='emoji-57', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 56 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823383_emojibatteryfood_14.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823383_emojibatteryfood_14.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 57: name='emoji battery_01', id='emoji-58', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 57 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245742_emojibattery_01.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245742_emojibattery_01.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 58: name='EmojiBattery_Heart_17', id='emoji-59', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 58 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736246031_EmojiBattery_Heart_17.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736246031_EmojiBattery_Heart_17.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 59: name='Emoji Battery Sticker3D_13', id='emoji-60', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 59 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-03-03/1740989496_EmojiBatterySticker3D_13.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-03-03/1740989496_EmojiBatterySticker3D_13.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 60: name='battery_cartoon_01', id='emoji-61', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 60 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_01.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_01.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 61: name='EmojiBattery_Heart_08', id='emoji-62', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 61 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-08/1736308486_EmojiBattery_Heart_08.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-08/1736308486_EmojiBattery_Heart_08.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 62: name='battery_cartoon_09', id='emoji-63', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 62 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746603942_battery_cartoon_09.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746603942_battery_cartoon_09.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 63: name='Emoji Battery Brainrot_14', id='emoji-64', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 63 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_14.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_14.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 64: name='emoji_battery_food_04', id='emoji-65', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 64 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823657_emoji_battery_food_04.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823657_emoji_battery_food_04.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 65: name='emoji battery_09', id='emoji-66', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 65 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245742_emojibattery_09.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245742_emojibattery_09.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 66: name='Emoji Battery_15', id='emoji-67', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 66 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840515_EmojiBattery_15.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840515_EmojiBattery_15.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 67: name='Emoji Battery Brainrot_15', id='emoji-68', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 67 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_15.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_15.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 68: name='emoji battery food_01', id='emoji-69', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 68 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823382_emojibatteryfood_01.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823382_emojibatteryfood_01.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 69: name='emoji battery_07', id='emoji-70', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 69 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245742_emojibattery_07.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245742_emojibattery_07.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 70: name='battery_cartoon_13', id='emoji-71', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 70 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_13.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_13.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 71: name='Emoji Battery Cartoon_02', id='emoji-72', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 71 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739328230_EmojiBatteryCartoon_02.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739328230_EmojiBatteryCartoon_02.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 72: name='battery_cartoon_12', id='emoji-73', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 72 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746603941_battery_cartoon_12.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746603941_battery_cartoon_12.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 73: name='Emoji Battery Emotion_01', id='emoji-74', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 73 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823856_EmojiBatteryEmotion_01.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823856_EmojiBatteryEmotion_01.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 74: name='EmojiBattery_Heart_09', id='emoji-75', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 74 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736246030_EmojiBattery_Heart_09.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736246030_EmojiBattery_Heart_09.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 75: name='emoji_battery_heart_07', id='emoji-76', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 75 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761611_emoji_battery_heart_07.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761611_emoji_battery_heart_07.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 76: name='Emoji Battery_7', id='emoji-77', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 76 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840514_EmojiBattery_7.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840514_EmojiBattery_7.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 77: name='emoji_battery_animal_03', id='emoji-78', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 77 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761938_emoji_battery_animal_03.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761938_emoji_battery_animal_03.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 78: name='emoji battery food_02', id='emoji-79', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 78 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823382_emojibatteryfood_02.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823382_emojibatteryfood_02.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 79: name='battery_cartoon_14', id='emoji-80', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 79 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746603941_battery_cartoon_14.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746603941_battery_cartoon_14.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 80: name='emoji_battery_food_07', id='emoji-81', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 80 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823657_emoji_battery_food_07.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823657_emoji_battery_food_07.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 81: name='emoji_battery_animal_02', id='emoji-82', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 81 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761938_emoji_battery_animal_02.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761938_emoji_battery_animal_02.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 82: name='Emoji Battery_6', id='emoji-83', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 82 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840514_EmojiBattery_6.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840514_EmojiBattery_6.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 83: name='Emoji Battery Sticker3D_18', id='emoji-84', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 83 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-03-03/1740989496_EmojiBatterySticker3D_18.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-03-03/1740989496_EmojiBatterySticker3D_18.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 84: name='EmojiBattery_Heart_05', id='emoji-85', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 84 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-08/1736308486_EmojiBattery_Heart_05.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-08/1736308486_EmojiBattery_Heart_05.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 85: name='Emoji Battery Brainrot_19', id='emoji-86', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 85 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_19.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_19.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 86: name='Emoji Battery_17', id='emoji-87', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 86 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840515_EmojiBattery_17.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840515_EmojiBattery_17.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 87: name='battery_cartoon_09', id='emoji-88', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 87 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_09.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_09.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 88: name='EmojiBattery_Cute_01', id='emoji-89', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 88 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736246028_EmojiBattery_Cute_01.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736246028_EmojiBattery_Cute_01.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 89: name='emoji_battery_cute_07', id='emoji-90', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 89 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761119_emoji_battery_cute_07.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761119_emoji_battery_cute_07.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 90: name='EmojiBattery_Heart_10', id='emoji-91', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 90 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-08/1736308487_EmojiBattery_Heart_10.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-08/1736308487_EmojiBattery_Heart_10.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 91: name='battery_cartoon_18', id='emoji-92', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 91 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746603942_battery_cartoon_18.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746603942_battery_cartoon_18.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 92: name='emoji battery food_06', id='emoji-93', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 92 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823382_emojibatteryfood_06.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823382_emojibatteryfood_06.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 93: name='emoji_battery_food_03', id='emoji-94', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 93 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823657_emoji_battery_food_03.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823657_emoji_battery_food_03.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 94: name='Emoji Battery Brainrot_20', id='emoji-95', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 94 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_20.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_20.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 95: name='battery_cartoon_07', id='emoji-96', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 95 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_07.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_07.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 96: name='battery_cartoon_20', id='emoji-97', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 96 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746603942_battery_cartoon_20.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746603942_battery_cartoon_20.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 97: name='emoji battery_04', id='emoji-98', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 97 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245742_emojibattery_04.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245742_emojibattery_04.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 98: name='emoji battery_01', id='emoji-99', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 98 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-08/1736322235_emojibattery_01.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-08/1736322235_emojibattery_01.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 99: name='Emoji Battery_18', id='emoji-100', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 99 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840515_EmojiBattery_18.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840515_EmojiBattery_18.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 100: name='battery_cartoon_10', id='emoji-101', premium=false
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 100 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_10.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_10.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 101: name='battery_cartoon_11', id='emoji-102', premium=true
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 101 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_11.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_11.png'
07-03 11:42:11.646 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 102: name='Emoji Battery Emotion_08', id='emoji-103', premium=false
07-03 11:42:11.647 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 102 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823857_EmojiBatteryEmotion_08.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823857_EmojiBatteryEmotion_08.png'
07-03 11:42:11.647 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 103: name='Emoji Battery Emotion_07', id='emoji-104', premium=false
07-03 11:42:11.647 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 103 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823857_EmojiBatteryEmotion_07.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823857_EmojiBatteryEmotion_07.png'
07-03 11:42:11.647 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 104: name='emoji_battery_animal_07', id='emoji-105', premium=false
07-03 11:42:11.647 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 104 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761939_emoji_battery_animal_07.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761939_emoji_battery_animal_07.png'
07-03 11:42:11.647 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 105: name='emoji battery food_08', id='emoji-106', premium=false
07-03 11:42:11.647 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 105 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823383_emojibatteryfood_08.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823383_emojibatteryfood_08.png'
07-03 11:42:11.647 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 106: name='Emoji Battery Emotion_02', id='emoji-107', premium=false
07-03 11:42:11.647 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 106 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823856_EmojiBatteryEmotion_02.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823856_EmojiBatteryEmotion_02.png'
07-03 11:42:11.647 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 107: name='emoji_battery_cute_03', id='emoji-108', premium=true
07-03 11:42:11.647 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 107 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761119_emoji_battery_cute_03.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761119_emoji_battery_cute_03.png'
07-03 11:42:11.647 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 108: name='emoji battery_17', id='emoji-109', premium=false
07-03 11:42:11.647 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 108 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245743_emojibattery_17.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245743_emojibattery_17.png'
07-03 11:42:11.647 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 109: name='emoji_battery_cute_06', id='emoji-110', premium=false
07-03 11:42:11.647 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 109 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761119_emoji_battery_cute_06.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761119_emoji_battery_cute_06.png'
07-03 11:42:11.647 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 110: name='emoji_battery_heart_08', id='emoji-111', premium=true
07-03 11:42:11.647 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 110 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761611_emoji_battery_heart_08.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-13/1736761611_emoji_battery_heart_08.png'
07-03 11:42:11.647 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 111: name='Emoji Battery Emotion_20', id='emoji-112', premium=false
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 111 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823859_EmojiBatteryEmotion_20.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823859_EmojiBatteryEmotion_20.png'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 112: name='Emoji Battery Emotion_17', id='emoji-113', premium=false
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 112 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823858_EmojiBatteryEmotion_17.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736823858_EmojiBatteryEmotion_17.png'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 113: name='emoji battery_03', id='emoji-114', premium=false
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 113 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245742_emojibattery_03.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736245742_emojibattery_03.png'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 114: name='Emoji Battery_28', id='emoji-115', premium=false
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Style 114 images: thumbnail='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840536_EmojiBattery_28.png', gallery='https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-14/1736840536_EmojiBattery_28.png'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: After search filtering: 115 styles
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: After additional filtering: 115 styles
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 0: name='Emoji Battery Cartoon_04', id='emoji-1'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 1: name='Emoji Battery Cartoon_01', id='emoji-2'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 2: name='Emoji Battery Brainrot_01', id='emoji-3'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 3: name='Emoji Battery Sticker3D_02', id='emoji-4'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 4: name='Emoji Battery Cartoon_13', id='emoji-5'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 5: name='Emoji Battery Brainrot_03', id='emoji-6'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 6: name='Emoji Battery Sticker3D_05', id='emoji-7'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 7: name='battery_cartoon_20', id='emoji-8'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 8: name='battery_cartoon_16', id='emoji-9'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 9: name='battery_cartoon_15', id='emoji-10'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 10: name='Emoji Battery Sticker3D_20', id='emoji-11'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 11: name='Emoji Battery Brainrot_04', id='emoji-12'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 12: name='Emoji Battery Brainrot_08', id='emoji-13'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 13: name='Emoji Battery Brainrot_06', id='emoji-14'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 14: name='Emoji Battery Sticker3D_07', id='emoji-15'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 15: name='emoji_battery_heart_04', id='emoji-16'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 16: name='battery_cartoon_18', id='emoji-17'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 17: name='Emoji Battery Cartoon_03', id='emoji-18'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 18: name='Emoji Battery Brainrot_05', id='emoji-19'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 19: name='Emoji Battery Cartoon_05', id='emoji-20'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 20: name='EmojiBattery_Heart_12', id='emoji-21'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 21: name='Emoji Battery Brainrot_11', id='emoji-22'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 22: name='emoji_battery_heart_01', id='emoji-23'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 23: name='Emoji Battery Cartoon_07', id='emoji-24'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 24: name='emoji_battery_heart_10', id='emoji-25'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 25: name='EmojiBattery_Heart_04', id='emoji-26'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 26: name='battery_cartoon_03', id='emoji-27'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 27: name='EmojiBattery', id='emoji-28'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 28: name='emoji_battery_animal_10', id='emoji-29'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 29: name='emoji battery_01-1', id='emoji-30'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 30: name='Emoji Battery_3', id='emoji-31'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 31: name='battery_cartoon_02', id='emoji-32'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 32: name='EmojiBattery_Cute_04', id='emoji-33'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 33: name='Emoji Battery Cartoon_10', id='emoji-34'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 34: name='emoji_battery_food_01', id='emoji-35'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 35: name='emoji battery_10', id='emoji-36'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 36: name='emoji_battery_cute_05', id='emoji-37'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 37: name='emoji battery food_12', id='emoji-38'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 38: name='Emoji Battery_13', id='emoji-39'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 39: name='emoji_battery_animal_09', id='emoji-40'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 40: name='EmojiBattery_Cute_10', id='emoji-41'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 41: name='battery_cartoon_03', id='emoji-42'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 42: name='Emoji Battery_1', id='emoji-43'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 43: name='EmojiBattery_Heart_07', id='emoji-44'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 44: name='emoji battery_20', id='emoji-45'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 45: name='EmojiBattery_Heart_13', id='emoji-46'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 46: name='emoji battery_16', id='emoji-47'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 47: name='emoji battery_15', id='emoji-48'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 48: name='Emoji Battery Cartoon_14', id='emoji-49'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 49: name='Emoji Battery Emotion_05', id='emoji-50'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 50: name='Emoji Battery Cartoon_12', id='emoji-51'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 51: name='EmojiBattery_Heart_06', id='emoji-52'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 52: name='battery_cartoon_06', id='emoji-53'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 53: name='Emoji Battery_9', id='emoji-54'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 54: name='Emoji Battery Emotion_11', id='emoji-55'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 55: name='battery_cartoon_07', id='emoji-56'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 56: name='emoji battery food_14', id='emoji-57'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 57: name='emoji battery_01', id='emoji-58'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 58: name='EmojiBattery_Heart_17', id='emoji-59'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 59: name='Emoji Battery Sticker3D_13', id='emoji-60'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 60: name='battery_cartoon_01', id='emoji-61'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 61: name='EmojiBattery_Heart_08', id='emoji-62'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 62: name='battery_cartoon_09', id='emoji-63'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 63: name='Emoji Battery Brainrot_14', id='emoji-64'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 64: name='emoji_battery_food_04', id='emoji-65'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 65: name='emoji battery_09', id='emoji-66'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 66: name='Emoji Battery_15', id='emoji-67'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 67: name='Emoji Battery Brainrot_15', id='emoji-68'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 68: name='emoji battery food_01', id='emoji-69'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 69: name='emoji battery_07', id='emoji-70'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 70: name='battery_cartoon_13', id='emoji-71'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 71: name='Emoji Battery Cartoon_02', id='emoji-72'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 72: name='battery_cartoon_12', id='emoji-73'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 73: name='Emoji Battery Emotion_01', id='emoji-74'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 74: name='EmojiBattery_Heart_09', id='emoji-75'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 75: name='emoji_battery_heart_07', id='emoji-76'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 76: name='Emoji Battery_7', id='emoji-77'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 77: name='emoji_battery_animal_03', id='emoji-78'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 78: name='emoji battery food_02', id='emoji-79'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 79: name='battery_cartoon_14', id='emoji-80'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 80: name='emoji_battery_food_07', id='emoji-81'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 81: name='emoji_battery_animal_02', id='emoji-82'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 82: name='Emoji Battery_6', id='emoji-83'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 83: name='Emoji Battery Sticker3D_18', id='emoji-84'
07-03 11:42:11.648 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 84: name='EmojiBattery_Heart_05', id='emoji-85'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 85: name='Emoji Battery Brainrot_19', id='emoji-86'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 86: name='Emoji Battery_17', id='emoji-87'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 87: name='battery_cartoon_09', id='emoji-88'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 88: name='EmojiBattery_Cute_01', id='emoji-89'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 89: name='emoji_battery_cute_07', id='emoji-90'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 90: name='EmojiBattery_Heart_10', id='emoji-91'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 91: name='battery_cartoon_18', id='emoji-92'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 92: name='emoji battery food_06', id='emoji-93'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 93: name='emoji_battery_food_03', id='emoji-94'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 94: name='Emoji Battery Brainrot_20', id='emoji-95'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 95: name='battery_cartoon_07', id='emoji-96'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 96: name='battery_cartoon_20', id='emoji-97'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 97: name='emoji battery_04', id='emoji-98'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 98: name='emoji battery_01', id='emoji-99'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 99: name='Emoji Battery_18', id='emoji-100'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 100: name='battery_cartoon_10', id='emoji-101'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 101: name='battery_cartoon_11', id='emoji-102'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 102: name='Emoji Battery Emotion_08', id='emoji-103'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 103: name='Emoji Battery Emotion_07', id='emoji-104'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 104: name='emoji_battery_animal_07', id='emoji-105'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 105: name='emoji battery food_08', id='emoji-106'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 106: name='Emoji Battery Emotion_02', id='emoji-107'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 107: name='emoji_battery_cute_03', id='emoji-108'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 108: name='emoji battery_17', id='emoji-109'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 109: name='emoji_battery_cute_06', id='emoji-110'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 110: name='emoji_battery_heart_08', id='emoji-111'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 111: name='Emoji Battery Emotion_20', id='emoji-112'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 112: name='Emoji Battery Emotion_17', id='emoji-113'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 113: name='emoji battery_03', id='emoji-114'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: Final style 114: name='Emoji Battery_28', id='emoji-115'
07-03 11:42:11.649 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: About to emit new state with 115 displayed styles
07-03 11:42:11.650 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: UI state collected - loading=false, styles=115
07-03 11:42:11.650 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: State details - allStyles=115, selectedCategory=HOT
07-03 11:42:11.650 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: State details - isInitialLoadComplete=true, errorMessage=null
07-03 11:42:11.650 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: updateUI called with state:
07-03 11:42:11.650 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - isLoading: false
07-03 11:42:11.650 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - displayedStyles count: 115
07-03 11:42:11.650 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - allStyles count: 115
07-03 11:42:11.650 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - selectedCategory: HOT
07-03 11:42:11.650 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - errorMessage: null
07-03 11:42:11.650 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - isInitialLoadComplete: true
07-03 11:42:11.650 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: - shouldRequestPermissions: false
07-03 11:42:11.650 21624 21624 D EmojiBatteryFragment: ERROR_UI: Hiding error state
07-03 11:42:11.650 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: updateBatteryStyles called with 115 styles
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Current adapter item count: 0
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 0: name='Emoji Battery Cartoon_04', id='emoji-1', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 1: name='Emoji Battery Cartoon_01', id='emoji-2', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 2: name='Emoji Battery Brainrot_01', id='emoji-3', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 3: name='Emoji Battery Sticker3D_02', id='emoji-4', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 4: name='Emoji Battery Cartoon_13', id='emoji-5', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 5: name='Emoji Battery Brainrot_03', id='emoji-6', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 6: name='Emoji Battery Sticker3D_05', id='emoji-7', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 7: name='battery_cartoon_20', id='emoji-8', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 8: name='battery_cartoon_16', id='emoji-9', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 9: name='battery_cartoon_15', id='emoji-10', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 10: name='Emoji Battery Sticker3D_20', id='emoji-11', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 11: name='Emoji Battery Brainrot_04', id='emoji-12', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 12: name='Emoji Battery Brainrot_08', id='emoji-13', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 13: name='Emoji Battery Brainrot_06', id='emoji-14', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 14: name='Emoji Battery Sticker3D_07', id='emoji-15', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 15: name='emoji_battery_heart_04', id='emoji-16', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 16: name='battery_cartoon_18', id='emoji-17', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 17: name='Emoji Battery Cartoon_03', id='emoji-18', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 18: name='Emoji Battery Brainrot_05', id='emoji-19', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 19: name='Emoji Battery Cartoon_05', id='emoji-20', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 20: name='EmojiBattery_Heart_12', id='emoji-21', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 21: name='Emoji Battery Brainrot_11', id='emoji-22', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 22: name='emoji_battery_heart_01', id='emoji-23', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 23: name='Emoji Battery Cartoon_07', id='emoji-24', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 24: name='emoji_battery_heart_10', id='emoji-25', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 25: name='EmojiBattery_Heart_04', id='emoji-26', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 26: name='battery_cartoon_03', id='emoji-27', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 27: name='EmojiBattery', id='emoji-28', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 28: name='emoji_battery_animal_10', id='emoji-29', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 29: name='emoji battery_01-1', id='emoji-30', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 30: name='Emoji Battery_3', id='emoji-31', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 31: name='battery_cartoon_02', id='emoji-32', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 32: name='EmojiBattery_Cute_04', id='emoji-33', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 33: name='Emoji Battery Cartoon_10', id='emoji-34', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 34: name='emoji_battery_food_01', id='emoji-35', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 35: name='emoji battery_10', id='emoji-36', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 36: name='emoji_battery_cute_05', id='emoji-37', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 37: name='emoji battery food_12', id='emoji-38', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 38: name='Emoji Battery_13', id='emoji-39', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 39: name='emoji_battery_animal_09', id='emoji-40', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 40: name='EmojiBattery_Cute_10', id='emoji-41', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 41: name='battery_cartoon_03', id='emoji-42', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 42: name='Emoji Battery_1', id='emoji-43', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 43: name='EmojiBattery_Heart_07', id='emoji-44', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 44: name='emoji battery_20', id='emoji-45', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 45: name='EmojiBattery_Heart_13', id='emoji-46', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 46: name='emoji battery_16', id='emoji-47', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 47: name='emoji battery_15', id='emoji-48', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 48: name='Emoji Battery Cartoon_14', id='emoji-49', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 49: name='Emoji Battery Emotion_05', id='emoji-50', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 50: name='Emoji Battery Cartoon_12', id='emoji-51', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 51: name='EmojiBattery_Heart_06', id='emoji-52', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 52: name='battery_cartoon_06', id='emoji-53', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 53: name='Emoji Battery_9', id='emoji-54', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 54: name='Emoji Battery Emotion_11', id='emoji-55', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 55: name='battery_cartoon_07', id='emoji-56', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 56: name='emoji battery food_14', id='emoji-57', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 57: name='emoji battery_01', id='emoji-58', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 58: name='EmojiBattery_Heart_17', id='emoji-59', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 59: name='Emoji Battery Sticker3D_13', id='emoji-60', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 60: name='battery_cartoon_01', id='emoji-61', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 61: name='EmojiBattery_Heart_08', id='emoji-62', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 62: name='battery_cartoon_09', id='emoji-63', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 63: name='Emoji Battery Brainrot_14', id='emoji-64', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 64: name='emoji_battery_food_04', id='emoji-65', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 65: name='emoji battery_09', id='emoji-66', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 66: name='Emoji Battery_15', id='emoji-67', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 67: name='Emoji Battery Brainrot_15', id='emoji-68', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 68: name='emoji battery food_01', id='emoji-69', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 69: name='emoji battery_07', id='emoji-70', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 70: name='battery_cartoon_13', id='emoji-71', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 71: name='Emoji Battery Cartoon_02', id='emoji-72', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 72: name='battery_cartoon_12', id='emoji-73', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 73: name='Emoji Battery Emotion_01', id='emoji-74', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 74: name='EmojiBattery_Heart_09', id='emoji-75', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 75: name='emoji_battery_heart_07', id='emoji-76', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 76: name='Emoji Battery_7', id='emoji-77', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 77: name='emoji_battery_animal_03', id='emoji-78', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 78: name='emoji battery food_02', id='emoji-79', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 79: name='battery_cartoon_14', id='emoji-80', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 80: name='emoji_battery_food_07', id='emoji-81', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 81: name='emoji_battery_animal_02', id='emoji-82', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 82: name='Emoji Battery_6', id='emoji-83', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 83: name='Emoji Battery Sticker3D_18', id='emoji-84', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 84: name='EmojiBattery_Heart_05', id='emoji-85', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 85: name='Emoji Battery Brainrot_19', id='emoji-86', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 86: name='Emoji Battery_17', id='emoji-87', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 87: name='battery_cartoon_09', id='emoji-88', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 88: name='EmojiBattery_Cute_01', id='emoji-89', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 89: name='emoji_battery_cute_07', id='emoji-90', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 90: name='EmojiBattery_Heart_10', id='emoji-91', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 91: name='battery_cartoon_18', id='emoji-92', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 92: name='emoji battery food_06', id='emoji-93', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 93: name='emoji_battery_food_03', id='emoji-94', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 94: name='Emoji Battery Brainrot_20', id='emoji-95', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 95: name='battery_cartoon_07', id='emoji-96', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 96: name='battery_cartoon_20', id='emoji-97', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 97: name='emoji battery_04', id='emoji-98', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 98: name='emoji battery_01', id='emoji-99', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 99: name='Emoji Battery_18', id='emoji-100', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 100: name='battery_cartoon_10', id='emoji-101', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 101: name='battery_cartoon_11', id='emoji-102', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 102: name='Emoji Battery Emotion_08', id='emoji-103', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 103: name='Emoji Battery Emotion_07', id='emoji-104', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 104: name='emoji_battery_animal_07', id='emoji-105', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 105: name='emoji battery food_08', id='emoji-106', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 106: name='Emoji Battery Emotion_02', id='emoji-107', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 107: name='emoji_battery_cute_03', id='emoji-108', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 108: name='emoji battery_17', id='emoji-109', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 109: name='emoji_battery_cute_06', id='emoji-110', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 110: name='emoji_battery_heart_08', id='emoji-111', premium=true
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 111: name='Emoji Battery Emotion_20', id='emoji-112', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 112: name='Emoji Battery Emotion_17', id='emoji-113', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 113: name='emoji battery_03', id='emoji-114', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Style 114: name='Emoji Battery_28', id='emoji-115', premium=false
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: RecyclerView adapter: com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter@fa3292a
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: RecyclerView visibility: 0
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: RecyclerView layout manager: androidx.recyclerview.widget.GridLayoutManager@f7f831b
07-03 11:42:11.651 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: updateBatteryStyles completed. Adapter now has 0 items
07-03 11:42:11.652 21624 21624 D EmojiBatteryFragment: Category selection UI state confirmed: HOT (index: 0)
07-03 11:42:11.652 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: updateUI completed successfully
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: EMOJI_VIEWMODEL: State emitted successfully. Current state has 115 displayed styles
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 0 in hot_category: Emoji Battery Cartoon_04 (id=emoji-1, priority=1, isPremium=true)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 1 in hot_category: Emoji Battery Cartoon_01 (id=emoji-2, priority=2, isPremium=true)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 2 in hot_category: Emoji Battery Brainrot_01 (id=emoji-3, priority=3, isPremium=true)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 3 in hot_category: Emoji Battery Sticker3D_02 (id=emoji-4, priority=4, isPremium=false)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 4 in hot_category: Emoji Battery Cartoon_13 (id=emoji-5, priority=5, isPremium=true)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 5 in hot_category: Emoji Battery Brainrot_03 (id=emoji-6, priority=6, isPremium=true)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 6 in hot_category: Emoji Battery Sticker3D_05 (id=emoji-7, priority=7, isPremium=true)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 7 in hot_category: battery_cartoon_20 (id=emoji-8, priority=8, isPremium=false)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 8 in hot_category: battery_cartoon_16 (id=emoji-9, priority=9, isPremium=true)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 9 in hot_category: battery_cartoon_15 (id=emoji-10, priority=10, isPremium=false)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 10 in hot_category: Emoji Battery Sticker3D_20 (id=emoji-11, priority=11, isPremium=false)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 11 in hot_category: Emoji Battery Brainrot_04 (id=emoji-12, priority=12, isPremium=true)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 12 in hot_category: Emoji Battery Brainrot_08 (id=emoji-13, priority=13, isPremium=false)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 13 in hot_category: Emoji Battery Brainrot_06 (id=emoji-14, priority=14, isPremium=false)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 14 in hot_category: Emoji Battery Sticker3D_07 (id=emoji-15, priority=15, isPremium=false)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 15 in hot_category: emoji_battery_heart_04 (id=emoji-16, priority=16, isPremium=true)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 16 in hot_category: battery_cartoon_18 (id=emoji-17, priority=17, isPremium=false)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 17 in hot_category: Emoji Battery Cartoon_03 (id=emoji-18, priority=18, isPremium=false)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 18 in hot_category: Emoji Battery Brainrot_05 (id=emoji-19, priority=19, isPremium=false)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 19 in hot_category: Emoji Battery Cartoon_05 (id=emoji-20, priority=20, isPremium=true)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 20 in hot_category: EmojiBattery_Heart_12 (id=emoji-21, priority=21, isPremium=true)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 21 in hot_category: Emoji Battery Brainrot_11 (id=emoji-22, priority=22, isPremium=true)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 22 in hot_category: emoji_battery_heart_01 (id=emoji-23, priority=23, isPremium=true)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 23 in hot_category: Emoji Battery Cartoon_07 (id=emoji-24, priority=24, isPremium=false)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 24 in hot_category: emoji_battery_heart_10 (id=emoji-25, priority=25, isPremium=false)
07-03 11:42:11.652 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 25 in hot_category: EmojiBattery_Heart_04 (id=emoji-26, priority=26, isPremium=true)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 26 in hot_category: battery_cartoon_03 (id=emoji-27, priority=27, isPremium=true)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 27 in hot_category: EmojiBattery (id=emoji-28, priority=28, isPremium=false)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 28 in hot_category: emoji_battery_animal_10 (id=emoji-29, priority=29, isPremium=false)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 29 in hot_category: emoji battery_01-1 (id=emoji-30, priority=30, isPremium=false)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 30 in hot_category: Emoji Battery_3 (id=emoji-31, priority=31, isPremium=false)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 31 in hot_category: battery_cartoon_02 (id=emoji-32, priority=32, isPremium=true)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 32 in hot_category: EmojiBattery_Cute_04 (id=emoji-33, priority=33, isPremium=false)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 33 in hot_category: Emoji Battery Cartoon_10 (id=emoji-34, priority=34, isPremium=true)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 34 in hot_category: emoji_battery_food_01 (id=emoji-35, priority=35, isPremium=false)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 35 in hot_category: emoji battery_10 (id=emoji-36, priority=36, isPremium=false)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 36 in hot_category: emoji_battery_cute_05 (id=emoji-37, priority=37, isPremium=true)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 37 in hot_category: emoji battery food_12 (id=emoji-38, priority=38, isPremium=false)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 38 in hot_category: Emoji Battery_13 (id=emoji-39, priority=39, isPremium=false)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 39 in hot_category: emoji_battery_animal_09 (id=emoji-40, priority=40, isPremium=true)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 40 in hot_category: EmojiBattery_Cute_10 (id=emoji-41, priority=41, isPremium=false)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 41 in hot_category: battery_cartoon_03 (id=emoji-42, priority=42, isPremium=true)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 42 in hot_category: Emoji Battery_1 (id=emoji-43, priority=43, isPremium=false)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 43 in hot_category: EmojiBattery_Heart_07 (id=emoji-44, priority=44, isPremium=true)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 44 in hot_category: emoji battery_20 (id=emoji-45, priority=45, isPremium=false)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 45 in hot_category: EmojiBattery_Heart_13 (id=emoji-46, priority=46, isPremium=true)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 46 in hot_category: emoji battery_16 (id=emoji-47, priority=47, isPremium=false)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 47 in hot_category: emoji battery_15 (id=emoji-48, priority=48, isPremium=false)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 48 in hot_category: Emoji Battery Cartoon_14 (id=emoji-49, priority=49, isPremium=true)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 49 in hot_category: Emoji Battery Emotion_05 (id=emoji-50, priority=50, isPremium=true)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 50 in hot_category: Emoji Battery Cartoon_12 (id=emoji-51, priority=51, isPremium=true)
07-03 11:42:11.653 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 51 in hot_category: EmojiBattery_Heart_06 (id=emoji-52, priority=52, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 52 in hot_category: battery_cartoon_06 (id=emoji-53, priority=53, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 53 in hot_category: Emoji Battery_9 (id=emoji-54, priority=54, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 54 in hot_category: Emoji Battery Emotion_11 (id=emoji-55, priority=55, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 55 in hot_category: battery_cartoon_07 (id=emoji-56, priority=56, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 56 in hot_category: emoji battery food_14 (id=emoji-57, priority=57, isPremium=true)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 57 in hot_category: emoji battery_01 (id=emoji-58, priority=58, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 58 in hot_category: EmojiBattery_Heart_17 (id=emoji-59, priority=59, isPremium=true)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 59 in hot_category: Emoji Battery Sticker3D_13 (id=emoji-60, priority=60, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 60 in hot_category: battery_cartoon_01 (id=emoji-61, priority=61, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 61 in hot_category: EmojiBattery_Heart_08 (id=emoji-62, priority=62, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 62 in hot_category: battery_cartoon_09 (id=emoji-63, priority=63, isPremium=true)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 63 in hot_category: Emoji Battery Brainrot_14 (id=emoji-64, priority=64, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 64 in hot_category: emoji_battery_food_04 (id=emoji-65, priority=65, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 65 in hot_category: emoji battery_09 (id=emoji-66, priority=66, isPremium=true)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 66 in hot_category: Emoji Battery_15 (id=emoji-67, priority=67, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 67 in hot_category: Emoji Battery Brainrot_15 (id=emoji-68, priority=68, isPremium=true)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 68 in hot_category: emoji battery food_01 (id=emoji-69, priority=69, isPremium=true)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 69 in hot_category: emoji battery_07 (id=emoji-70, priority=70, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 70 in hot_category: battery_cartoon_13 (id=emoji-71, priority=71, isPremium=true)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 71 in hot_category: Emoji Battery Cartoon_02 (id=emoji-72, priority=72, isPremium=true)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 72 in hot_category: battery_cartoon_12 (id=emoji-73, priority=73, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 73 in hot_category: Emoji Battery Emotion_01 (id=emoji-74, priority=74, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 74 in hot_category: EmojiBattery_Heart_09 (id=emoji-75, priority=75, isPremium=true)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 75 in hot_category: emoji_battery_heart_07 (id=emoji-76, priority=76, isPremium=true)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 76 in hot_category: Emoji Battery_7 (id=emoji-77, priority=77, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 77 in hot_category: emoji_battery_animal_03 (id=emoji-78, priority=78, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 78 in hot_category: emoji battery food_02 (id=emoji-79, priority=79, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 79 in hot_category: battery_cartoon_14 (id=emoji-80, priority=80, isPremium=true)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 80 in hot_category: emoji_battery_food_07 (id=emoji-81, priority=81, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 81 in hot_category: emoji_battery_animal_02 (id=emoji-82, priority=82, isPremium=true)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 82 in hot_category: Emoji Battery_6 (id=emoji-83, priority=83, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 83 in hot_category: Emoji Battery Sticker3D_18 (id=emoji-84, priority=84, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 84 in hot_category: EmojiBattery_Heart_05 (id=emoji-85, priority=85, isPremium=false)
07-03 11:42:11.654 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 85 in hot_category: Emoji Battery Brainrot_19 (id=emoji-86, priority=86, isPremium=true)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 86 in hot_category: Emoji Battery_17 (id=emoji-87, priority=87, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 87 in hot_category: battery_cartoon_09 (id=emoji-88, priority=88, isPremium=true)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 88 in hot_category: EmojiBattery_Cute_01 (id=emoji-89, priority=89, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 89 in hot_category: emoji_battery_cute_07 (id=emoji-90, priority=90, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 90 in hot_category: EmojiBattery_Heart_10 (id=emoji-91, priority=91, isPremium=true)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 91 in hot_category: battery_cartoon_18 (id=emoji-92, priority=92, isPremium=true)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 92 in hot_category: emoji battery food_06 (id=emoji-93, priority=93, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 93 in hot_category: emoji_battery_food_03 (id=emoji-94, priority=94, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 94 in hot_category: Emoji Battery Brainrot_20 (id=emoji-95, priority=95, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 95 in hot_category: battery_cartoon_07 (id=emoji-96, priority=96, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 96 in hot_category: battery_cartoon_20 (id=emoji-97, priority=97, isPremium=true)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 97 in hot_category: emoji battery_04 (id=emoji-98, priority=98, isPremium=true)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 98 in hot_category: emoji battery_01 (id=emoji-99, priority=99, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 99 in hot_category: Emoji Battery_18 (id=emoji-100, priority=100, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 100 in hot_category: battery_cartoon_10 (id=emoji-101, priority=101, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 101 in hot_category: battery_cartoon_11 (id=emoji-102, priority=102, isPremium=true)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 102 in hot_category: Emoji Battery Emotion_08 (id=emoji-103, priority=103, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 103 in hot_category: Emoji Battery Emotion_07 (id=emoji-104, priority=104, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 104 in hot_category: emoji_battery_animal_07 (id=emoji-105, priority=105, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 105 in hot_category: emoji battery food_08 (id=emoji-106, priority=106, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 106 in hot_category: Emoji Battery Emotion_02 (id=emoji-107, priority=107, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 107 in hot_category: emoji_battery_cute_03 (id=emoji-108, priority=108, isPremium=true)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 108 in hot_category: emoji battery_17 (id=emoji-109, priority=109, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 109 in hot_category: emoji_battery_cute_06 (id=emoji-110, priority=110, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 110 in hot_category: emoji_battery_heart_08 (id=emoji-111, priority=111, isPremium=true)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 111 in hot_category: Emoji Battery Emotion_20 (id=emoji-112, priority=112, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 112 in hot_category: Emoji Battery Emotion_17 (id=emoji-113, priority=113, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 113 in hot_category: emoji battery_03 (id=emoji-114, priority=114, isPremium=false)
07-03 11:42:11.655 21624 21624 D BatteryGalleryVM: REMOTE_CONFIG: Item 114 in hot_category: Emoji Battery_28 (id=emoji-115, priority=115, isPremium=false)
07-03 11:42:11.714 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Post-layout - Width: 964, Height: 0
07-03 11:42:11.715 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Post-layout - Child count: 0
07-03 11:42:11.715 21624 21624 D EmojiBatteryFragment: EMOJI_FRAGMENT: Post-layout - Adapter item count: 0
07-03 11:42:11.718 21624 21624 D BatteryApplication: AD_ADAPTER_LOAD: Rewarded Ad loading initiated (parallel)
07-03 11:42:11.824 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Creating new ViewHolder
07-03 11:42:11.836 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: ViewHolder created successfully
07-03 11:42:11.837 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Binding item at position 0: name='Emoji Battery Cartoon_04', id='emoji-1'
07-03 11:42:11.837 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Binding position 0 - Emoji Battery Cartoon_04
07-03 11:42:11.837 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Layout params: width=-1, height=-1
07-03 11:42:11.837 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView dimensions: width=0, height=0
07-03 11:42:11.837 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView visibility: 0
07-03 11:42:11.837 21624 21624 D BatteryStyleViewHolder: Loading gallery image for style: Emoji Battery Cartoon_04, URL: https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739327485_EmojiBatteryCartoon_04.png
07-03 11:42:11.853 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Successfully bound battery style at position 0: Emoji Battery Cartoon_04, premium: true
07-03 11:42:11.853 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Successfully bound item at position 0
07-03 11:42:11.853 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Creating new ViewHolder
07-03 11:42:11.856 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: ViewHolder created successfully
07-03 11:42:11.857 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Binding item at position 1: name='Emoji Battery Cartoon_01', id='emoji-2'
07-03 11:42:11.858 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Binding position 1 - Emoji Battery Cartoon_01
07-03 11:42:11.858 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Layout params: width=-1, height=-1
07-03 11:42:11.858 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView dimensions: width=0, height=0
07-03 11:42:11.858 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView visibility: 0
07-03 11:42:11.858 21624 21624 D BatteryStyleViewHolder: Loading gallery image for style: Emoji Battery Cartoon_01, URL: https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739327485_EmojiBatteryCartoon_01.png
07-03 11:42:11.858 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Successfully bound battery style at position 1: Emoji Battery Cartoon_01, premium: true
07-03 11:42:11.859 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Successfully bound item at position 1
07-03 11:42:11.859 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Creating new ViewHolder
07-03 11:42:11.866 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: ViewHolder created successfully
07-03 11:42:11.866 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Binding item at position 2: name='Emoji Battery Brainrot_01', id='emoji-3'
07-03 11:42:11.866 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Binding position 2 - Emoji Battery Brainrot_01
07-03 11:42:11.866 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Layout params: width=-1, height=-1
07-03 11:42:11.866 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView dimensions: width=0, height=0
07-03 11:42:11.866 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView visibility: 0
07-03 11:42:11.866 21624 21624 D BatteryStyleViewHolder: Loading gallery image for style: Emoji Battery Brainrot_01, URL: https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_01.png
07-03 11:42:11.867 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Successfully bound battery style at position 2: Emoji Battery Brainrot_01, premium: true
07-03 11:42:11.867 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Successfully bound item at position 2
07-03 11:42:11.875 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Creating new ViewHolder
07-03 11:42:11.878 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: ViewHolder created successfully
07-03 11:42:11.878 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Binding item at position 3: name='Emoji Battery Sticker3D_02', id='emoji-4'
07-03 11:42:11.879 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Binding position 3 - Emoji Battery Sticker3D_02
07-03 11:42:11.879 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Layout params: width=-1, height=-1
07-03 11:42:11.879 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView dimensions: width=0, height=0
07-03 11:42:11.879 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView visibility: 0
07-03 11:42:11.879 21624 21624 D BatteryStyleViewHolder: Loading gallery image for style: Emoji Battery Sticker3D_02, URL: https://cdn-uploader.innovatex.one/thumnails/800/store/2025-03-03/1740989496_EmojiBatterySticker3D_02.png
07-03 11:42:11.879 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Successfully bound battery style at position 3: Emoji Battery Sticker3D_02, premium: false
07-03 11:42:11.879 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Successfully bound item at position 3
07-03 11:42:11.879 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Creating new ViewHolder
07-03 11:42:11.881 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: ViewHolder created successfully
07-03 11:42:11.881 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Binding item at position 4: name='Emoji Battery Cartoon_13', id='emoji-5'
07-03 11:42:11.881 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Binding position 4 - Emoji Battery Cartoon_13
07-03 11:42:11.881 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Layout params: width=-1, height=-1
07-03 11:42:11.881 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView dimensions: width=0, height=0
07-03 11:42:11.881 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView visibility: 0
07-03 11:42:11.881 21624 21624 D BatteryStyleViewHolder: Loading gallery image for style: Emoji Battery Cartoon_13, URL: https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-12/1739327486_EmojiBatteryCartoon_13.png
07-03 11:42:11.882 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Successfully bound battery style at position 4: Emoji Battery Cartoon_13, premium: true
07-03 11:42:11.882 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Successfully bound item at position 4
07-03 11:42:11.882 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Creating new ViewHolder
07-03 11:42:11.884 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: ViewHolder created successfully
07-03 11:42:11.884 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Binding item at position 5: name='Emoji Battery Brainrot_03', id='emoji-6'
07-03 11:42:11.884 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Binding position 5 - Emoji Battery Brainrot_03
07-03 11:42:11.884 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Layout params: width=-1, height=-1
07-03 11:42:11.884 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView dimensions: width=0, height=0
07-03 11:42:11.884 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView visibility: 0
07-03 11:42:11.884 21624 21624 D BatteryStyleViewHolder: Loading gallery image for style: Emoji Battery Brainrot_03, URL: https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_03.png
07-03 11:42:11.884 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Successfully bound battery style at position 5: Emoji Battery Brainrot_03, premium: true
07-03 11:42:11.884 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Successfully bound item at position 5
07-03 11:42:11.890 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Creating new ViewHolder
07-03 11:42:11.892 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: ViewHolder created successfully
07-03 11:42:11.892 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Binding item at position 6: name='Emoji Battery Sticker3D_05', id='emoji-7'
07-03 11:42:11.892 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Binding position 6 - Emoji Battery Sticker3D_05
07-03 11:42:11.893 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Layout params: width=-1, height=-1
07-03 11:42:11.893 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView dimensions: width=0, height=0
07-03 11:42:11.893 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView visibility: 0
07-03 11:42:11.893 21624 21624 D BatteryStyleViewHolder: Loading gallery image for style: Emoji Battery Sticker3D_05, URL: https://cdn-uploader.innovatex.one/thumnails/800/store/2025-03-03/1740989496_EmojiBatterySticker3D_05.png
07-03 11:42:11.893 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Successfully bound battery style at position 6: Emoji Battery Sticker3D_05, premium: true
07-03 11:42:11.893 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Successfully bound item at position 6
07-03 11:42:11.893 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Creating new ViewHolder
07-03 11:42:11.895 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: ViewHolder created successfully
07-03 11:42:11.895 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Binding item at position 7: name='battery_cartoon_20', id='emoji-8'
07-03 11:42:11.895 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Binding position 7 - battery_cartoon_20
07-03 11:42:11.895 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Layout params: width=-1, height=-1
07-03 11:42:11.895 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView dimensions: width=0, height=0
07-03 11:42:11.895 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView visibility: 0
07-03 11:42:11.895 21624 21624 D BatteryStyleViewHolder: Loading gallery image for style: battery_cartoon_20, URL: https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_20.png
07-03 11:42:11.896 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Successfully bound battery style at position 7: battery_cartoon_20, premium: false
07-03 11:42:11.896 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Successfully bound item at position 7
07-03 11:42:11.896 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Creating new ViewHolder
07-03 11:42:11.898 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: ViewHolder created successfully
07-03 11:42:11.899 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Binding item at position 8: name='battery_cartoon_16', id='emoji-9'
07-03 11:42:11.899 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Binding position 8 - battery_cartoon_16
07-03 11:42:11.899 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Layout params: width=-1, height=-1
07-03 11:42:11.899 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView dimensions: width=0, height=0
07-03 11:42:11.899 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView visibility: 0
07-03 11:42:11.899 21624 21624 D BatteryStyleViewHolder: Loading gallery image for style: battery_cartoon_16, URL: https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_16.png
07-03 11:42:11.899 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Successfully bound battery style at position 8: battery_cartoon_16, premium: true
07-03 11:42:11.899 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Successfully bound item at position 8
07-03 11:42:11.905 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Creating new ViewHolder
07-03 11:42:11.907 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: ViewHolder created successfully
07-03 11:42:11.908 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Binding item at position 9: name='battery_cartoon_15', id='emoji-10'
07-03 11:42:11.908 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Binding position 9 - battery_cartoon_15
07-03 11:42:11.908 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Layout params: width=-1, height=-1
07-03 11:42:11.908 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView dimensions: width=0, height=0
07-03 11:42:11.908 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView visibility: 0
07-03 11:42:11.908 21624 21624 D BatteryStyleViewHolder: Loading gallery image for style: battery_cartoon_15, URL: https://cdn-uploader.innovatex.one/thumnails/800/store/2025-02-28/1740728677_battery_cartoon_15.png
07-03 11:42:11.908 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Successfully bound battery style at position 9: battery_cartoon_15, premium: false
07-03 11:42:11.908 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Successfully bound item at position 9
07-03 11:42:11.908 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Creating new ViewHolder
07-03 11:42:11.910 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: ViewHolder created successfully
07-03 11:42:11.910 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Binding item at position 10: name='Emoji Battery Sticker3D_20', id='emoji-11'
07-03 11:42:11.910 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Binding position 10 - Emoji Battery Sticker3D_20
07-03 11:42:11.910 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Layout params: width=-1, height=-1
07-03 11:42:11.910 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView dimensions: width=0, height=0
07-03 11:42:11.910 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView visibility: 0
07-03 11:42:11.910 21624 21624 D BatteryStyleViewHolder: Loading gallery image for style: Emoji Battery Sticker3D_20, URL: https://cdn-uploader.innovatex.one/thumnails/800/store/2025-03-03/1740989496_EmojiBatterySticker3D_20.png
07-03 11:42:11.911 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Successfully bound battery style at position 10: Emoji Battery Sticker3D_20, premium: false
07-03 11:42:11.911 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Successfully bound item at position 10
07-03 11:42:11.911 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Creating new ViewHolder
07-03 11:42:11.913 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: ViewHolder created successfully
07-03 11:42:11.913 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Binding item at position 11: name='Emoji Battery Brainrot_04', id='emoji-12'
07-03 11:42:11.913 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Binding position 11 - Emoji Battery Brainrot_04
07-03 11:42:11.913 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Layout params: width=-1, height=-1
07-03 11:42:11.913 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView dimensions: width=0, height=0
07-03 11:42:11.913 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: ItemView visibility: 0
07-03 11:42:11.913 21624 21624 D BatteryStyleViewHolder: Loading gallery image for style: Emoji Battery Brainrot_04, URL: https://cdn-uploader.innovatex.one/thumnails/800/store/2025-05-07/1746605073_EmojiBatteryBrainrot_04.png
07-03 11:42:11.914 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Successfully bound battery style at position 11: Emoji Battery Brainrot_04, premium: true
07-03 11:42:11.914 21624 21624 D BatteryStyleAdapter: EMOJI_ADAPTER: Successfully bound item at position 11
07-03 11:42:11.971 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Post-bind dimensions for position 0: width=311, height=312
07-03 11:42:11.971 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Post-bind dimensions for position 1: width=301, height=312
07-03 11:42:11.971 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Post-bind dimensions for position 2: width=312, height=312
07-03 11:42:11.971 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Post-bind dimensions for position 3: width=311, height=312
07-03 11:42:11.972 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Post-bind dimensions for position 4: width=301, height=312
07-03 11:42:11.972 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Post-bind dimensions for position 5: width=312, height=312
07-03 11:42:11.972 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Post-bind dimensions for position 6: width=311, height=312
07-03 11:42:11.972 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Post-bind dimensions for position 7: width=301, height=312
07-03 11:42:11.972 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Post-bind dimensions for position 8: width=312, height=312
07-03 11:42:11.972 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Post-bind dimensions for position 9: width=311, height=312
07-03 11:42:11.972 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Post-bind dimensions for position 10: width=301, height=312
07-03 11:42:11.972 21624 21624 D BatteryStyleViewHolder: EMOJI_ADAPTER: Post-bind dimensions for position 11: width=312, height=312
07-03 11:42:12.113 21624 21624 D BatteryStyleViewHolder: Image loaded successfully for style: Emoji Battery Cartoon_04
07-03 11:42:12.133 21624 21624 D BatteryStyleViewHolder: Image loaded successfully for style: Emoji Battery Cartoon_01
07-03 11:42:12.157 21624 21624 D BatteryStyleViewHolder: Image loaded successfully for style: Emoji Battery Brainrot_01
07-03 11:42:12.165 21624 21624 D BatteryStyleViewHolder: Image loaded successfully for style: Emoji Battery Sticker3D_02
07-03 11:42:12.188 21624 21624 D BatteryStyleViewHolder: Image loaded successfully for style: Emoji Battery Cartoon_13
07-03 11:42:12.216 21624 21624 D BatteryStyleViewHolder: Image loaded successfully for style: Emoji Battery Brainrot_03
07-03 11:42:12.248 21624 21624 D BatteryStyleViewHolder: Image loaded successfully for style: Emoji Battery Sticker3D_05
07-03 11:42:12.268 21624 21624 D BatteryStyleViewHolder: Image loaded successfully for style: battery_cartoon_20
07-03 11:42:12.282 21624 21624 D BatteryStyleViewHolder: Image loaded successfully for style: battery_cartoon_16
07-03 11:42:12.298 21624 21624 D BatteryStyleViewHolder: Image loaded successfully for style: battery_cartoon_15
07-03 11:42:12.317 21624 21624 D BatteryStyleViewHolder: Image loaded successfully for style: Emoji Battery Sticker3D_20
07-03 11:42:12.344 21624 21624 D BatteryStyleViewHolder: Image loaded successfully for style: Emoji Battery Brainrot_04
