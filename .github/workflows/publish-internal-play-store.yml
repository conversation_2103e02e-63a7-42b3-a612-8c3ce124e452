name: Android Internal Play Store

env:
  # The name of the main module repository
  main_project_module: app

  # The name of the Play Store
  playstore_name: <PERSON><PERSON><PERSON>

on:
  push:
    branches:
      - 'release/**'

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set Up JDK
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu' # See 'Supported distributions' for available options
          java-version: '17'
          cache: 'gradle'

      # Cache Gradle dependencies and build cache
      - name: Cache Gradle dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
            ~/.gradle/buildOutputCleanup
          key: gradle-${{ runner.os }}-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            gradle-${{ runner.os }}-

      # Cache Android build cache
      - name: Cache Android build cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.android/build-cache
          key: android-build-cache-${{ runner.os }}-${{ hashFiles('**/*.gradle*') }}
          restore-keys: |
            android-build-cache-${{ runner.os }}-

      - name: Change wrapper permissions
        run: chmod +x ./gradlew

      - name: Build Project
        run: ./gradlew build -x lint

      - name: Build Release AAB
        run: ./gradlew bundleRelease

      - name: Sign AAB
        uses: r0adkll/sign-android-release@v1
        with:
          releaseDirectory: app/build/outputs/bundle/release
          signingKeyBase64: ${{ secrets.SIGN_KEY }}
          alias: ${{ secrets.KEY_ALIAS }}
          keyStorePassword: ${{ secrets.KEYSTORE_PASSWORD }}
          keyPassword: ${{ secrets.KEY_PASSWORD }}

      - name: Deploy to Play Store
        uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJsonPlainText: ${{secrets.SERVICE_ACCOUNT}}
          packageName: com.fc.p.tj.charginganimation.batterycharging.chargeeffect
          releaseFiles: app/build/outputs/bundle/release/app-release.aab
          track: qa