package com.tqhit.battery.one.features.emoji.presentation.customize.adapter

import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.ItemEmojiComponentBinding
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle

/**
 * DiffUtil callback for efficient emoji component list updates.
 * Compares battery styles by ID and content to determine what changed.
 */
private class EmojiComponentDiffCallback : DiffUtil.ItemCallback<BatteryStyle>() {
    override fun areItemsTheSame(oldItem: BatteryStyle, newItem: BatteryStyle): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(oldItem: BatteryStyle, newItem: BatteryStyle): Boolean {
        return oldItem == newItem
    }
}

/**
 * Specialized RecyclerView adapter for displaying emoji components in the customize screen.
 * This adapter is designed specifically for Phase 1 of the emoji carousel feature.
 * 
 * Key Features:
 * - Displays only the emoji character image (emojiImageUrl) from BatteryStyle objects
 * - Uses ListAdapter with DiffUtil for efficient updates
 * - Implements selection state management for mix-and-match functionality
 * - Optimized for horizontal carousel layout
 * - Follows established patterns from BatteryStyleAdapter but specialized for component display
 */
class EmojiComponentAdapter(
    private val parentContext: Context,
    private val onEmojiComponentClick: (BatteryStyle) -> Unit,
    private val onPremiumUnlock: (BatteryStyle) -> Unit
) : ListAdapter<BatteryStyle, EmojiComponentViewHolder>(EmojiComponentDiffCallback()) {
    
    companion object {
        private const val TAG = "EmojiComponentAdapter"
    }
    
    private var selectedEmojiId: String? = null
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): EmojiComponentViewHolder {
        Log.d(TAG, "EMOJI_COMPONENT_ADAPTER: Creating new ViewHolder")
        val binding = ItemEmojiComponentBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        Log.d(TAG, "EMOJI_COMPONENT_ADAPTER: ViewHolder created successfully")
        return EmojiComponentViewHolder(binding)
    }

    override fun onBindViewHolder(holder: EmojiComponentViewHolder, position: Int) {
        if (position >= itemCount) {
            Log.e(TAG, "EMOJI_COMPONENT_ADAPTER: Invalid position $position for items size $itemCount")
            return
        }

        val item = getItem(position)
        val isSelected = item.id == selectedEmojiId
        
        Log.d(TAG, "EMOJI_COMPONENT_ADAPTER: Binding emoji component at position $position: name='${item.name}', id='${item.id}', selected=$isSelected")
        holder.bind(
            item,
            parentContext,
            isSelected,
            onEmojiComponentClick,
            onPremiumUnlock,
            position
        )
        Log.d(TAG, "EMOJI_COMPONENT_ADAPTER: Successfully bound emoji component at position $position")
    }
    
    /**
     * Updates the selected emoji component and refreshes the UI
     */
    fun updateSelection(selectedEmojiId: String?) {
        val oldSelectedId = this.selectedEmojiId
        this.selectedEmojiId = selectedEmojiId
        
        Log.d(TAG, "EMOJI_COMPONENT_ADAPTER: Selection updated from '$oldSelectedId' to '$selectedEmojiId'")
        
        // Refresh items that were previously selected or are now selected
        currentList.forEachIndexed { index, item ->
            if (item.id == oldSelectedId || item.id == selectedEmojiId) {
                notifyItemChanged(index)
            }
        }
    }
}

/**
 * ViewHolder for emoji component items in the customize carousel.
 * Handles emoji character image loading and selection state display.
 */
class EmojiComponentViewHolder(
    private val binding: ItemEmojiComponentBinding
) : RecyclerView.ViewHolder(binding.root) {
    
    companion object {
        private const val TAG = "EmojiComponentViewHolder"
    }
    
    fun bind(
        item: BatteryStyle,
        parentContext: Context,
        isSelected: Boolean,
        onEmojiComponentClick: (BatteryStyle) -> Unit,
        onPremiumUnlock: (BatteryStyle) -> Unit,
        position: Int
    ) {
        try {
            Log.d(TAG, "EMOJI_COMPONENT_ADAPTER: Binding position $position - ${item.name}, selected: $isSelected")
            
            // Start shimmer loading effect
            binding.shimmerLayout.startShimmer()
            binding.shimmerLayout.visibility = View.VISIBLE
            
            // Show/hide premium indicator
            binding.lockBtn.visibility = if (item.isPremium) View.VISIBLE else View.GONE
            
            // Update selection state
            updateSelectionState(isSelected)
            
            // Load emoji character image only (not the combined style image)
            loadEmojiImage(item)
            
            // Set click listeners
            setupClickListeners(item, onEmojiComponentClick, onPremiumUnlock, position)
            
            Log.d(TAG, "EMOJI_COMPONENT_ADAPTER: Successfully bound emoji component at position $position: ${item.name}, premium: ${item.isPremium}, selected: $isSelected")
            
        } catch (exception: Exception) {
            Log.e(TAG, "EMOJI_COMPONENT_ADAPTER: Error binding emoji component at position $position", exception)
        }
    }
    
    private fun updateSelectionState(isSelected: Boolean) {
        // Update selection border/background
        if (isSelected) {
            binding.componentContainer.setBackgroundResource(R.drawable.bg_component_selected)
            binding.selectionIndicator.visibility = View.VISIBLE
        } else {
            binding.componentContainer.setBackgroundResource(R.drawable.bg_component_default)
            binding.selectionIndicator.visibility = View.GONE
        }
    }
    
    private fun loadEmojiImage(item: BatteryStyle) {
        val imageUrl = item.emojiImageUrl
        
        Log.d(TAG, "EMOJI_COMPONENT_ADAPTER: Loading emoji image: $imageUrl")
        
        val requestOptions = RequestOptions()
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .placeholder(R.drawable.placeholder_emoji_component)
            .error(R.drawable.error_emoji_component)
        
        Glide.with(binding.root.context)
            .load(imageUrl)
            .apply(requestOptions)
            .into(binding.emojiImage)
            .also {
                // Stop shimmer when image loads
                binding.shimmerLayout.stopShimmer()
                binding.shimmerLayout.visibility = View.GONE
            }
    }
    
    private fun setupClickListeners(
        item: BatteryStyle,
        onEmojiComponentClick: (BatteryStyle) -> Unit,
        onPremiumUnlock: (BatteryStyle) -> Unit,
        position: Int
    ) {
        binding.componentContainer.setOnClickListener {
            Log.d(TAG, "EMOJI_COMPONENT_ADAPTER: Emoji component clicked at position $position: ${item.name}")
            
            if (item.isPremium) {
                Log.d(TAG, "EMOJI_COMPONENT_ADAPTER: Premium emoji component clicked, triggering unlock flow")
                onPremiumUnlock(item)
            } else {
                Log.d(TAG, "EMOJI_COMPONENT_ADAPTER: Free emoji component clicked, triggering selection")
                onEmojiComponentClick(item)
            }
        }
        
        binding.lockBtn.setOnClickListener {
            Log.d(TAG, "EMOJI_COMPONENT_ADAPTER: Premium lock button clicked for: ${item.name}")
            onPremiumUnlock(item)
        }
    }
}
