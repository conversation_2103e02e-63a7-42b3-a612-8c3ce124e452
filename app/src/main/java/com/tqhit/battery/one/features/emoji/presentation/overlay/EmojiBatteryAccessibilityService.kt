package com.tqhit.battery.one.features.emoji.presentation.overlay

import android.accessibilityservice.AccessibilityService
import android.content.Intent
import android.content.res.Configuration
import android.graphics.PixelFormat
import android.os.Build
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityEvent
import com.tqhit.battery.one.R
import kotlin.math.abs
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * Accessibility service for displaying emoji battery overlay.
 * Uses TYPE_ACCESSIBILITY_OVERLAY for API 26+ and fallback for older versions.
 *
 * This service follows the established patterns in the app:
 * - Uses Hilt for dependency injection
 * - Integrates with CoreBatteryStatsProvider for battery data
 * - Uses BatteryLogger for comprehensive logging
 * - Follows proper lifecycle management
 * - Handles API level compatibility
 * - Supports swipe gestures for notification panel access
 * - Avoids conflicts with ChargingOverlayService
 */
@AndroidEntryPoint
class EmojiBatteryAccessibilityService : AccessibilityService() {

    companion object {
        private const val TAG = "EmojiBatteryAccessibilityService"
        private const val EMOJI_SERVICE_TAG = "EmojiService_Lifecycle"
        private const val EMOJI_OVERLAY_TAG = "EmojiService_Overlay"
        private const val EMOJI_BATTERY_TAG = "EmojiService_Battery"
        private const val EMOJI_CONFIG_TAG = "EmojiService_Config"

        // Intent actions for service control
        const val ACTION_SHOW_OVERLAY = "com.tqhit.battery.one.emoji.SHOW_OVERLAY"
        const val ACTION_HIDE_OVERLAY = "com.tqhit.battery.one.emoji.HIDE_OVERLAY"
        const val ACTION_UPDATE_CONFIG = "com.tqhit.battery.one.emoji.UPDATE_CONFIG"

        // Service state tracking
        @Volatile
        private var emojiServiceInstance: EmojiBatteryAccessibilityService? = null

        /**
         * Gets the current emoji service instance if running
         */
        fun getInstance(): EmojiBatteryAccessibilityService? {
            BatteryLogger.d(EMOJI_SERVICE_TAG, "Getting emoji service instance: ${emojiServiceInstance != null}")
            return emojiServiceInstance
        }

        /**
         * Checks if the emoji service is currently running
         */
        fun isServiceRunning(): Boolean {
            val isRunning = emojiServiceInstance != null
            BatteryLogger.d(EMOJI_SERVICE_TAG, "Emoji service running status: $isRunning")
            return isRunning
        }
    }
    
    // Dependencies
    @Inject
    lateinit var coreBatteryStatsProvider: CoreBatteryStatsProvider
    
    @Inject
    lateinit var customizationRepository: CustomizationRepository
    
    // UI components
    private var emojiWindowManager: WindowManager? = null
    private var emojiOverlayView: EmojiBatteryView? = null
    private var emojiLayoutParams: WindowManager.LayoutParams? = null

    // State management
    private var isEmojiOverlayVisible = false
    private var emojiBatteryStatusJob: Job? = null
    private var emojiCustomizationJob: Job? = null
    private var currentEmojiConfig: CustomizationConfig? = null

    // Status bar update coroutine job - Modernized from Timer to fix threading issues
    private var statusBarUpdateJob: Job? = null

    // Coroutine scope for service operations
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    // Touch event handling - REMOVED: Now handled exclusively by EmojiBatteryView's GestureDetector
    // Eliminated: emojiInitialTouchY, emojiInitialTouchX, emojiTouchStartTime properties
    // Eliminated: handleEmojiTouchEvent() method 
    // This reduces redundant touch processing and creates single source of truth in EmojiBatteryView

    override fun onCreate() {
        super.onCreate()
        BatteryLogger.d(TAG, "EmojiBatteryAccessibilityService onCreate")
        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_CREATED")

        emojiServiceInstance = this

        emojiWindowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        setupEmojiOverlayView()

        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_ONCREATE_COMPLETED")
    }
    
    override fun onServiceConnected() {
        super.onServiceConnected()
        BatteryLogger.d(TAG, "EmojiBatteryAccessibilityService connected")
        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_CONNECTED")

        startEmojiBatteryMonitoring()
        startEmojiCustomizationMonitoring()
        showEmojiOverlayIfEnabled()

        // Start status bar update coroutine
        startStatusBarUpdater()

        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_CONNECTION_COMPLETED")
    }

    override fun onDestroy() {
        BatteryLogger.d(TAG, "EmojiBatteryAccessibilityService onDestroy")
        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_DESTROY_STARTED")

        stopEmojiMonitoring()
        stopStatusBarUpdater()
        hideEmojiOverlay()
        cleanupEmojiService()

        emojiServiceInstance = null

        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_DESTROYED")
        super.onDestroy()
    }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        // We don't need to handle accessibility events for this emoji overlay
        // This service is primarily used for overlay permissions
        BatteryLogger.v(EMOJI_SERVICE_TAG, "Accessibility event received (ignored for emoji overlay)")
    }

    override fun onInterrupt() {
        BatteryLogger.d(TAG, "EmojiBatteryAccessibilityService interrupted")
        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_INTERRUPTED")
        hideEmojiOverlay()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        BatteryLogger.d(TAG, "EmojiBatteryAccessibilityService configuration changed")
        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_CONFIG_CHANGED")

        // Handle orientation changes, theme changes, and screen density changes
        if (isEmojiOverlayVisible) {
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "Updating overlay for configuration change")

            // Update theme and positioning
            emojiOverlayView?.updateTheme()

            // Recreate overlay with new positioning and theme
            hideEmojiOverlay()
            setupEmojiOverlayView()
            showEmojiOverlay()
        }

        // Update emoji overlay configuration if needed
        updateEmojiConfiguration()

        // Update status bar info
        updateStatusBarInfo()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val action = intent?.action ?: "null"
        BatteryLogger.d(TAG, "EmojiBatteryAccessibilityService onStartCommand - action: $action")
        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_COMMAND_RECEIVED: $action")

        when (intent?.action) {
            ACTION_SHOW_OVERLAY -> {
                BatteryLogger.d(EMOJI_OVERLAY_TAG, "Show emoji overlay command received")
                showEmojiOverlay()
            }
            ACTION_HIDE_OVERLAY -> {
                BatteryLogger.d(EMOJI_OVERLAY_TAG, "Hide emoji overlay command received")
                hideEmojiOverlay()
            }
            ACTION_UPDATE_CONFIG -> {
                BatteryLogger.d(EMOJI_CONFIG_TAG, "Update emoji config command received")
                updateEmojiConfiguration()
            }
            else -> {
                BatteryLogger.w(EMOJI_SERVICE_TAG, "Unknown action received: $action")
            }
        }

        return START_STICKY
    }

    /**
     * Sets up the emoji overlay view and layout parameters
     */
    private fun setupEmojiOverlayView() {
        BatteryLogger.d(TAG, "Setting up emoji overlay view")
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_SETUP_STARTED")

        try {
            // EmojiBatteryView now handles all touch events internally via its GestureDetector
            // No need for service-level touch listener - eliminates redundant touch handling
            emojiOverlayView = EmojiBatteryView(this)

            emojiLayoutParams = createEmojiLayoutParams()
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_SETUP_COMPLETED")
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "Touch handling delegated to EmojiBatteryView's internal GestureDetector")
        } catch (exception: Exception) {
            BatteryLogger.e(TAG, "Error setting up emoji overlay view", exception)
            BatteryLogger.e(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_SETUP_FAILED: ${exception.message}")
        }
    }
    
    /**
     * Creates layout parameters for the emoji overlay window
     * Positions the overlay as full-width status bar replacement
     */
    private fun createEmojiLayoutParams(): WindowManager.LayoutParams {
        val type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_PHONE
        }

        BatteryLogger.d(EMOJI_OVERLAY_TAG, "LAYOUT_PARAMS_CREATION_STARTED - API ${Build.VERSION.SDK_INT}, type: $type")

        // Get screen dimensions for full-width positioning
        val displayMetrics = resources.displayMetrics
        val screenWidth = displayMetrics.widthPixels
        val screenHeight = displayMetrics.heightPixels
        val statusBarHeight = getStatusBarHeight()

        BatteryLogger.d(EMOJI_OVERLAY_TAG, "SCREEN_DIMENSIONS: width=$screenWidth, height=$screenHeight, statusBarHeight=$statusBarHeight")

        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,  // Use MATCH_PARENT for true full width
            WindowManager.LayoutParams.WRAP_CONTENT,
            type,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,  // Allow positioning outside safe areas
            PixelFormat.TRANSLUCENT
        ).apply {
            // Position at top of screen, center horizontally
            gravity = Gravity.TOP or Gravity.CENTER_HORIZONTAL
            x = 0  // Center horizontally (with CENTER_HORIZONTAL gravity)
            y = resources.getDimensionPixelSize(R.dimen.emoji_overlay_margin_top)
            
            // Set specific height for status bar integration
            height = resources.getDimensionPixelSize(R.dimen.emoji_overlay_height)

            BatteryLogger.d(EMOJI_OVERLAY_TAG, "LAYOUT_PARAMS_CREATED: x=$x, y=$y, width=MATCH_PARENT, height=$height")
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "LAYOUT_PARAMS_FLAGS: gravity=${gravity}, flags=${flags}")
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "OVERLAY_POSITIONING: Center horizontally with full device width coverage")
        }
    }

    /**
     * Gets the status bar height for proper overlay positioning
     */
    private fun getStatusBarHeight(): Int {
        var statusBarHeight = 0
        val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            statusBarHeight = resources.getDimensionPixelSize(resourceId)
        }

        // Fallback to default status bar height if resource not found
        if (statusBarHeight == 0) {
            statusBarHeight = (24 * resources.displayMetrics.density).toInt() // 24dp default
        }

        BatteryLogger.d(EMOJI_OVERLAY_TAG, "Status bar height: $statusBarHeight px")
        return statusBarHeight
    }

    /**
     * Updates the status bar information in the overlay
     */
    private fun updateStatusBarInfo() {
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "STATUS_BAR_UPDATE_STARTED")
        try {
            val statusBarInfo = CustomStatusBarInfo.fromSystemState(this)
            emojiOverlayView?.updateStatusBarInfo(statusBarInfo)
            
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "STATUS_BAR_UPDATE_COMPLETED: time=${statusBarInfo.currentTime}")
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "STATUS_BAR_ICONS_STATE: wifi=${statusBarInfo.hasWifiConnection}, cellular=${statusBarInfo.hasCellularConnection}, silent=${statusBarInfo.isRingerSilent}")
        } catch (exception: Exception) {
            BatteryLogger.e(EMOJI_OVERLAY_TAG, "STATUS_BAR_UPDATE_ERROR", exception)
        }
    }

    /**
     * Starts the status bar update coroutine to refresh time and status info.
     * Modernized from Timer to fix threading issues and maintain coroutine consistency.
     */
    private fun startStatusBarUpdater() {
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "Starting status bar update coroutine")

        statusBarUpdateJob?.cancel()
        statusBarUpdateJob = serviceScope.launch {
            while (isActive) {
                // Update status bar info on main thread to avoid ViewRootImpl threading issues
                withContext(Dispatchers.Main) {
                    updateStatusBarInfo()
                }
                
                // Wait 30 seconds before next update
                delay(30_000L)
            }
        }

        BatteryLogger.d(EMOJI_OVERLAY_TAG, "Status bar update coroutine started")
    }

    /**
     * Stops the status bar update coroutine
     */
    private fun stopStatusBarUpdater() {
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "Stopping status bar update coroutine")

        statusBarUpdateJob?.cancel()
        statusBarUpdateJob = null

        BatteryLogger.d(EMOJI_OVERLAY_TAG, "Status bar update coroutine stopped")
    }
    
    /**
     * Starts monitoring emoji battery status changes
     */
    private fun startEmojiBatteryMonitoring() {
        BatteryLogger.d(TAG, "Starting emoji battery monitoring")
        BatteryLogger.d(EMOJI_BATTERY_TAG, "EMOJI_BATTERY_MONITORING_START")

        emojiBatteryStatusJob?.cancel()
        emojiBatteryStatusJob = serviceScope.launch {
            coreBatteryStatsProvider.coreBatteryStatusFlow
                .catch { exception ->
                    BatteryLogger.e(TAG, "Error in emoji battery status flow", exception)
                    BatteryLogger.e(EMOJI_BATTERY_TAG, "EMOJI_BATTERY_FLOW_ERROR: ${exception.message}")
                }
                .collect { status ->
                    BatteryLogger.d(EMOJI_BATTERY_TAG, "Emoji battery status update: ${status?.percentage}%, charging: ${status?.isCharging}")
                    status?.let { updateEmojiBatteryStatus(it) }
                }
        }
        BatteryLogger.d(EMOJI_BATTERY_TAG, "EMOJI_BATTERY_MONITORING_STARTED")
    }

    /**
     * Starts monitoring emoji customization configuration changes
     */
    private fun startEmojiCustomizationMonitoring() {
        BatteryLogger.d(TAG, "Starting emoji customization monitoring")
        BatteryLogger.d(EMOJI_CONFIG_TAG, "EMOJI_CONFIG_MONITORING_START")

        emojiCustomizationJob?.cancel()
        emojiCustomizationJob = serviceScope.launch {
            customizationRepository.getCustomizationConfigFlow()
                .catch { exception ->
                    BatteryLogger.e(TAG, "Error in emoji customization config flow", exception)
                    BatteryLogger.e(EMOJI_CONFIG_TAG, "EMOJI_CONFIG_FLOW_ERROR: ${exception.message}")
                }
                .collect { config ->
                    BatteryLogger.d(EMOJI_CONFIG_TAG, "Emoji config update: global enabled: ${config.isGlobalEnabled}")
                    updateEmojiCustomizationConfig(config)
                }
        }
        BatteryLogger.d(EMOJI_CONFIG_TAG, "EMOJI_CONFIG_MONITORING_STARTED")
    }
    
    /**
     * Updates the emoji battery status in the overlay view
     */
    private fun updateEmojiBatteryStatus(status: CoreBatteryStatus) {
        BatteryLogger.logBatteryStatus(
            EMOJI_BATTERY_TAG,
            status.percentage,
            status.isCharging,
            status.currentMicroAmperes,
            status.voltageMillivolts,
            status.temperatureCelsius
        )

        emojiOverlayView?.updateBatteryStatus(status)
        BatteryLogger.d(EMOJI_BATTERY_TAG, "EMOJI_BATTERY_STATUS_UPDATED: ${status.percentage}%, charging: ${status.isCharging}")
    }

    /**
     * Updates the emoji customization configuration
     */
    private fun updateEmojiCustomizationConfig(config: CustomizationConfig) {
        BatteryLogger.d(EMOJI_CONFIG_TAG, "Updating emoji customization config")
        currentEmojiConfig = config
        emojiOverlayView?.updateCustomizationConfig(config)

        // Show or hide emoji overlay based on global enabled state
        if (config.isGlobalEnabled && !isEmojiOverlayVisible) {
            BatteryLogger.d(EMOJI_CONFIG_TAG, "Config enabled and overlay not visible, showing emoji overlay")
            showEmojiOverlay()
        } else if (!config.isGlobalEnabled && isEmojiOverlayVisible) {
            BatteryLogger.d(EMOJI_CONFIG_TAG, "Config disabled and overlay visible, hiding emoji overlay")
            hideEmojiOverlay()
        }

        BatteryLogger.d(EMOJI_CONFIG_TAG, "EMOJI_CONFIG_UPDATED: global enabled: ${config.isGlobalEnabled}")
    }
    
    /**
     * Shows the emoji overlay if enabled in configuration
     */
    private fun showEmojiOverlayIfEnabled() {
        BatteryLogger.d(TAG, "Checking if emoji overlay should be shown")
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_ENABLED_CHECK")

        serviceScope.launch {
            try {
                val config = customizationRepository.getCustomizationConfig()
                BatteryLogger.d(EMOJI_CONFIG_TAG, "Retrieved config - global enabled: ${config.isGlobalEnabled}")
                if (config.isGlobalEnabled) {
                    BatteryLogger.d(EMOJI_OVERLAY_TAG, "Config enabled, showing emoji overlay")
                    showEmojiOverlay()
                } else {
                    BatteryLogger.d(EMOJI_OVERLAY_TAG, "Config disabled, not showing emoji overlay")
                }
            } catch (exception: Exception) {
                BatteryLogger.e(TAG, "Error checking if emoji overlay should be shown", exception)
                BatteryLogger.e(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_ENABLED_CHECK_FAILED: ${exception.message}")
            }
        }
    }

    /**
     * Shows the emoji overlay view
     */
    private fun showEmojiOverlay() {
        BatteryLogger.d(TAG, "Showing emoji overlay")
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_SHOW_REQUESTED")

        if (isEmojiOverlayVisible || emojiOverlayView == null || emojiLayoutParams == null) {
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "Emoji overlay show skipped - visible: $isEmojiOverlayVisible, view: ${emojiOverlayView != null}, params: ${emojiLayoutParams != null}")
            return
        }

        try {
            emojiWindowManager?.addView(emojiOverlayView, emojiLayoutParams)
            isEmojiOverlayVisible = true

            BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_SHOWN")
        } catch (exception: Exception) {
            BatteryLogger.e(TAG, "Error showing emoji overlay", exception)
            BatteryLogger.e(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_SHOW_FAILED: ${exception.message}")
        }
    }
    
    /**
     * Hides the emoji overlay view
     */
    private fun hideEmojiOverlay() {
        BatteryLogger.d(TAG, "Hiding emoji overlay")
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_HIDE_REQUESTED")

        if (!isEmojiOverlayVisible || emojiOverlayView == null) {
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "Emoji overlay hide skipped - visible: $isEmojiOverlayVisible, view: ${emojiOverlayView != null}")
            return
        }

        try {
            emojiWindowManager?.removeView(emojiOverlayView)
            isEmojiOverlayVisible = false
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_HIDDEN")
        } catch (exception: Exception) {
            BatteryLogger.e(TAG, "Error hiding emoji overlay", exception)
            BatteryLogger.e(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_HIDE_FAILED: ${exception.message}")
        }
    }

    /**
     * Updates emoji configuration from repository
     */
    private fun updateEmojiConfiguration() {
        BatteryLogger.d(TAG, "Updating emoji configuration from repository")
        BatteryLogger.d(EMOJI_CONFIG_TAG, "EMOJI_CONFIG_REPOSITORY_UPDATE")

        serviceScope.launch {
            try {
                val config = customizationRepository.getCustomizationConfig()
                BatteryLogger.d(EMOJI_CONFIG_TAG, "Retrieved updated config from repository")
                updateEmojiCustomizationConfig(config)
            } catch (exception: Exception) {
                BatteryLogger.e(TAG, "Error updating emoji configuration", exception)
                BatteryLogger.e(EMOJI_CONFIG_TAG, "EMOJI_CONFIG_REPOSITORY_UPDATE_FAILED: ${exception.message}")
            }
        }
    }
    
    // REMOVED: handleEmojiTouchEvent() method
    // Touch handling is now exclusively managed by EmojiBatteryView's internal GestureDetector
    // This eliminates code duplication and creates a single source of truth for gesture detection

    /**
     * Opens the notification panel using system intent
     */
    private fun openNotificationPanel() {
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "Opening notification panel via system intent")
        try {
            // Method 1: Try using system intent
            val intent = Intent("android.intent.action.OPEN_NOTIFICATION_PANEL")
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            startActivity(intent)
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "Notification panel intent sent")
        } catch (exception: Exception) {
            BatteryLogger.w(EMOJI_OVERLAY_TAG, "Could not open notification panel via intent: ${exception.message}")
            
            try {
                // Method 2: Try using accessibility service to perform global action
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                    performGlobalAction(GLOBAL_ACTION_NOTIFICATIONS)
                    BatteryLogger.d(EMOJI_OVERLAY_TAG, "Notification panel opened via global action")
                }
            } catch (exception2: Exception) {
                BatteryLogger.e(EMOJI_OVERLAY_TAG, "Could not open notification panel via global action: ${exception2.message}")
            }
        }
    }

    /**
     * Stops all emoji monitoring jobs
     */
    private fun stopEmojiMonitoring() {
        BatteryLogger.d(TAG, "Stopping emoji monitoring")
        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_MONITORING_STOP")

        emojiBatteryStatusJob?.cancel()
        emojiCustomizationJob?.cancel()
        emojiBatteryStatusJob = null
        emojiCustomizationJob = null

        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_MONITORING_STOPPED")
    }

    /**
     * Cleans up emoji service resources
     */
    private fun cleanupEmojiService() {
        BatteryLogger.d(TAG, "Cleaning up emoji service resources")
        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_CLEANUP_STARTED")

        emojiOverlayView?.cleanup()
        emojiOverlayView = null
        emojiLayoutParams = null
        emojiWindowManager = null
        currentEmojiConfig = null

        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_CLEANUP_COMPLETED")
    }
    

}
