package com.tqhit.battery.one.features.emoji.presentation.overlay.permission

import android.accessibilityservice.AccessibilityServiceInfo
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.view.accessibility.AccessibilityManager
import androidx.activity.result.ActivityResultLauncher
import com.tqhit.battery.one.R
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.features.emoji.presentation.overlay.EmojiBatteryAccessibilityService
import com.tqhit.battery.one.utils.BatteryLogger
import com.tqhit.battery.one.utils.OverlayPermissionUtils

/**
 * Manages permissions required for the emoji overlay feature.
 * Handles both accessibility service and system alert window permissions
 * with user-friendly dialogs and proper API level compatibility.
 * 
 * This manager follows the established patterns in the app:
 * - Uses existing permission utilities and dialog systems
 * - Provides clear user explanations for permission requests
 * - Handles API level differences gracefully
 * - Integrates with existing notification dialog system
 */
object EmojiOverlayPermissionManager {

    private const val TAG = "EmojiOverlayPermissionManager"
    private const val EMOJI_PERMISSION_TAG = "EmojiPermission_Check"
    private const val EMOJI_REQUEST_TAG = "EmojiPermission_Request"
    private const val EMOJI_DIALOG_TAG = "EmojiPermission_Dialog"
    
    /**
     * Checks if all required permissions are granted for the emoji overlay feature.
     *
     * @param context The application context
     * @return true if all required permissions are granted
     */
    fun hasAllRequiredPermissions(context: Context): Boolean {
        BatteryLogger.d(EMOJI_PERMISSION_TAG, "Checking all required emoji permissions")

        val hasAccessibilityPermission = isAccessibilityServiceEnabled(context)
        val hasOverlayPermission = if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            OverlayPermissionUtils.isOverlayPermissionGranted(context)
        } else {
            true // Not needed for API 26+
        }

        val allGranted = hasAccessibilityPermission && hasOverlayPermission
        BatteryLogger.d(EMOJI_PERMISSION_TAG, "EMOJI_PERMISSION_CHECK: Accessibility: $hasAccessibilityPermission, Overlay: $hasOverlayPermission, All granted: $allGranted")

        return allGranted
    }
    
    /**
     * Checks if the emoji accessibility service is enabled for our app.
     *
     * @param context The application context
     * @return true if the emoji accessibility service is enabled
     */
    fun isAccessibilityServiceEnabled(context: Context): Boolean {
        BatteryLogger.d(EMOJI_PERMISSION_TAG, "Checking emoji accessibility service status")

        val accessibilityManager = context.getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager
        val enabledServices = accessibilityManager.getEnabledAccessibilityServiceList(AccessibilityServiceInfo.FEEDBACK_ALL_MASK)

        val serviceName = "${context.packageName}/${EmojiBatteryAccessibilityService::class.java.name}"
        val isEnabled = enabledServices.any { it.id == serviceName }

        BatteryLogger.d(EMOJI_PERMISSION_TAG, "EMOJI_ACCESSIBILITY_SERVICE_CHECK: enabled: $isEnabled for service: $serviceName")
        return isEnabled
    }
    
    /**
     * Requests all required permissions with user-friendly explanations.
     * 
     * @param context The activity context
     * @param onAllPermissionsGranted Callback when all permissions are granted
     * @param onPermissionsDenied Callback when permissions are denied
     */
    fun requestRequiredPermissions(
        context: Context,
        onAllPermissionsGranted: () -> Unit = {},
        onPermissionsDenied: () -> Unit = {}
    ) {
        BatteryLogger.d(TAG, "Requesting required emoji permissions")
        BatteryLogger.d(EMOJI_REQUEST_TAG, "EMOJI_PERMISSION_REQUEST_STARTED")

        if (hasAllRequiredPermissions(context)) {
            BatteryLogger.d(EMOJI_REQUEST_TAG, "All emoji permissions already granted")
            onAllPermissionsGranted()
            return
        }

        // Check which permissions are needed
        val needsAccessibility = !isAccessibilityServiceEnabled(context)
        val needsOverlay = Build.VERSION.SDK_INT < Build.VERSION_CODES.O &&
                          !OverlayPermissionUtils.isOverlayPermissionGranted(context)

        BatteryLogger.d(EMOJI_REQUEST_TAG, "Permission needs - accessibility: $needsAccessibility, overlay: $needsOverlay")

        when {
            needsAccessibility -> requestAccessibilityPermission(context, onAllPermissionsGranted, onPermissionsDenied)
            needsOverlay -> requestOverlayPermission(context, onAllPermissionsGranted, onPermissionsDenied)
            else -> onAllPermissionsGranted()
        }
    }
    
    /**
     * Requests emoji accessibility service permission with explanation dialog.
     */
    private fun requestAccessibilityPermission(
        context: Context,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        BatteryLogger.d(TAG, "Requesting emoji accessibility permission")
        BatteryLogger.d(EMOJI_REQUEST_TAG, "EMOJI_ACCESSIBILITY_PERMISSION_REQUEST")

        val title = context.getString(R.string.emoji_overlay_accessibility_permission_title)
        val message = context.getString(R.string.emoji_overlay_accessibility_permission_message)

        BatteryLogger.d(EMOJI_DIALOG_TAG, "Showing emoji accessibility permission dialog")
        NotificationDialog(
            context = context,
            title = title,
            message = message,
            onConfirm = {
                BatteryLogger.d(EMOJI_DIALOG_TAG, "User confirmed emoji accessibility permission request")
                openAccessibilitySettings(context)
                // Note: We can't directly detect when permission is granted from settings
                // The calling code should handle this in onResume or similar lifecycle method
            },
            onCancel = {
                BatteryLogger.d(EMOJI_DIALOG_TAG, "User cancelled emoji accessibility permission request")
                onDenied()
            }
        ).show()
    }
    
    /**
     * Requests emoji overlay permission for older Android versions.
     */
    private fun requestOverlayPermission(
        context: Context,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        BatteryLogger.d(TAG, "Requesting emoji overlay permission for Android < 8.0")
        BatteryLogger.d(EMOJI_REQUEST_TAG, "EMOJI_OVERLAY_PERMISSION_REQUEST")

        OverlayPermissionUtils.requestOverlayPermissionIfNeeded(
            context = context,
            onPermissionGranted = {
                BatteryLogger.d(EMOJI_REQUEST_TAG, "Emoji overlay permission granted, checking accessibility")
                // Check if we still need accessibility permission
                if (!isAccessibilityServiceEnabled(context)) {
                    requestAccessibilityPermission(context, onGranted, onDenied)
                } else {
                    onGranted()
                }
            },
            onPermissionDenied = {
                BatteryLogger.d(EMOJI_REQUEST_TAG, "Emoji overlay permission denied")
                onDenied()
            }
        )
    }
    
    /**
     * Opens the accessibility settings page for the user to enable the emoji service.
     */
    private fun openAccessibilitySettings(context: Context) {
        BatteryLogger.d(TAG, "Opening accessibility settings for emoji service")
        BatteryLogger.d(EMOJI_REQUEST_TAG, "EMOJI_ACCESSIBILITY_SETTINGS_OPEN")

        try {
            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
            BatteryLogger.d(EMOJI_REQUEST_TAG, "Emoji accessibility settings opened successfully")
        } catch (exception: Exception) {
            BatteryLogger.e(TAG, "Error opening emoji accessibility settings", exception)

            // Fallback to general settings
            try {
                val fallbackIntent = Intent(Settings.ACTION_SETTINGS).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                context.startActivity(fallbackIntent)
                BatteryLogger.d(EMOJI_REQUEST_TAG, "Opened general settings as fallback for emoji")
            } catch (fallbackException: Exception) {
                BatteryLogger.e(TAG, "Error opening fallback settings for emoji", fallbackException)
            }
        }
    }
    
    /**
     * Shows a comprehensive explanation dialog for why emoji permissions are needed.
     */
    fun showPermissionExplanationDialog(
        context: Context,
        onProceed: () -> Unit = {},
        onCancel: () -> Unit = {}
    ) {
        BatteryLogger.d(TAG, "Showing emoji permission explanation dialog")
        BatteryLogger.d(EMOJI_DIALOG_TAG, "EMOJI_PERMISSION_EXPLANATION_DIALOG")

        val title = context.getString(R.string.emoji_overlay_permission_explanation_title)
        val message = buildPermissionExplanationMessage(context)

        NotificationDialog(
            context = context,
            title = title,
            message = message,
            onConfirm = {
                BatteryLogger.d(EMOJI_DIALOG_TAG, "User confirmed emoji permission explanation")
                onProceed()
            },
            onCancel = {
                BatteryLogger.d(EMOJI_DIALOG_TAG, "User cancelled emoji permission explanation")
                onCancel()
            }
        ).show()
    }
    
    /**
     * Builds a detailed explanation message for permission requirements.
     */
    private fun buildPermissionExplanationMessage(context: Context): String {
        val baseMessage = context.getString(R.string.emoji_overlay_permission_explanation_message)
        
        val permissionsNeeded = mutableListOf<String>()
        
        // Always need accessibility service
        permissionsNeeded.add(context.getString(R.string.emoji_overlay_accessibility_permission_explanation))
        
        // Only need overlay permission for older Android versions
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            permissionsNeeded.add(context.getString(R.string.emoji_overlay_overlay_permission_explanation))
        }
        
        val permissionsList = permissionsNeeded.joinToString("\n\n• ", "\n\n• ")
        
        return "$baseMessage$permissionsList"
    }
    
    /**
     * Checks emoji permissions and shows appropriate dialogs or proceeds with action.
     */
    fun checkPermissionsAndProceed(
        context: Context,
        onAllPermissionsGranted: () -> Unit,
        onPermissionsDenied: () -> Unit = {},
        showExplanation: Boolean = true
    ) {
        BatteryLogger.d(TAG, "Checking emoji permissions and proceeding")
        BatteryLogger.d(EMOJI_PERMISSION_TAG, "EMOJI_PERMISSION_CHECK_AND_PROCEED")

        if (hasAllRequiredPermissions(context)) {
            BatteryLogger.d(EMOJI_PERMISSION_TAG, "All emoji permissions granted, proceeding")
            onAllPermissionsGranted()
            return
        }

        BatteryLogger.d(EMOJI_PERMISSION_TAG, "Emoji permissions missing, showing explanation: $showExplanation")
        if (showExplanation) {
            showPermissionExplanationDialog(
                context = context,
                onProceed = {
                    requestRequiredPermissions(context, onAllPermissionsGranted, onPermissionsDenied)
                },
                onCancel = onPermissionsDenied
            )
        } else {
            requestRequiredPermissions(context, onAllPermissionsGranted, onPermissionsDenied)
        }
    }

    /**
     * Gets a summary of current emoji permission status for debugging.
     */
    fun getPermissionStatusSummary(context: Context): Map<String, Any> {
        BatteryLogger.d(EMOJI_PERMISSION_TAG, "Getting emoji permission status summary")

        val summary = mapOf(
            "hasAllRequiredPermissions" to hasAllRequiredPermissions(context),
            "isAccessibilityServiceEnabled" to isAccessibilityServiceEnabled(context),
            "isOverlayPermissionGranted" to OverlayPermissionUtils.isOverlayPermissionGranted(context),
            "androidApiLevel" to Build.VERSION.SDK_INT,
            "needsOverlayPermission" to (Build.VERSION.SDK_INT < Build.VERSION_CODES.O),
            "serviceName" to "${context.packageName}/${EmojiBatteryAccessibilityService::class.java.name}"
        )

        BatteryLogger.d(EMOJI_PERMISSION_TAG, "EMOJI_PERMISSION_STATUS: $summary")
        return summary
    }
}
