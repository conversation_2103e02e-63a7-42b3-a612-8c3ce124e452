package com.tqhit.battery.one.features.emoji.presentation.customize

import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig

/**
 * UI state for the emoji battery customization screen.
 * Follows the established MVI pattern used throughout the app.
 * 
 * This state manages:
 * - Selected battery style and customization options
 * - Live preview configuration
 * - UI interaction states (loading, error, etc.)
 * - Form validation and user input
 */
data class CustomizeState(
    // Core data
    val selectedStyle: BatteryStyle? = null,
    val customizationConfig: CustomizationConfig = CustomizationConfig.createDefault(),
    val availableBatteryStyles: List<BatteryStyle> = emptyList(),
    val availableEmojiStyles: List<BatteryStyle> = emptyList(),

    // Phase 1: Component-specific selections for mix-and-match functionality
    val selectedBatteryStyle: BatteryStyle? = null,
    val selectedEmojiStyle: BatteryStyle? = null,
    
    // Live preview
    val previewConfig: BatteryStyleConfig = BatteryStyleConfig.createDefault(),
    val previewBatteryLevel: Int = 50, // Sample battery level for preview
    
    // UI states
    val isLoading: Boolean = false,
    val isInitialLoadComplete: Boolean = false,
    val isSaving: Boolean = false,
    val hasError: Boolean = false,
    val errorMessage: String = "",
    
    // Global feature state
    val isGlobalEnabled: Boolean = false,
    
    // Form states
    val showEmojiToggle: Boolean = true,
    val showPercentageToggle: Boolean = true,
    val percentageFontSize: Int = 14,
    val emojiSizeScale: Float = 1.0f,
    val percentageColor: Int = 0xFFFFFFFF.toInt(),
    
    // Color picker state
    val isColorPickerVisible: Boolean = false,
    val selectedColorIndex: Int = 0,
    val availableColors: List<Int> = getDefaultColors(),
    
    // Navigation state
    val shouldNavigateBack: Boolean = false,
    val shouldShowSuccessMessage: Boolean = false,
    val isCustomizationApplied: Boolean = false,
    
    // Validation
    val isFormValid: Boolean = true,
    val validationErrors: List<String> = emptyList()
) {
    
    /**
     * Checks if the screen is in a loading state.
     */
    val isAnyLoading: Boolean
        get() = isLoading || isSaving
    
    /**
     * Checks if the preview can be displayed.
     */
    val canShowPreview: Boolean
        get() = selectedStyle != null && !hasError
    
    /**
     * Checks if the apply button should be enabled.
     */
    val canApplyChanges: Boolean
        get() = selectedStyle != null && isFormValid && !isAnyLoading && !hasError
    
    /**
     * Gets the current preview configuration with all user customizations.
     */
    val currentPreviewConfig: BatteryStyleConfig
        get() = BatteryStyleConfig(
            showEmoji = showEmojiToggle,
            showPercentage = showPercentageToggle,
            percentageFontSizeDp = percentageFontSize,
            emojiSizeScale = emojiSizeScale,
            percentageColor = percentageColor
        )
    
    companion object {
        /**
         * Default color palette for percentage text.
         */
        fun getDefaultColors(): List<Int> = listOf(
            0xFFFFFFFF.toInt(), // White
            0xFF000000.toInt(), // Black
            0xFFFF0000.toInt(), // Red
            0xFF00FF00.toInt(), // Green
            0xFF0000FF.toInt(), // Blue
            0xFFFFFF00.toInt(), // Yellow
            0xFFFF00FF.toInt(), // Magenta
            0xFF00FFFF.toInt(), // Cyan
            0xFFFF8000.toInt(), // Orange
            0xFF8000FF.toInt(), // Purple
            0xFF808080.toInt(), // Gray
            0xFF800000.toInt()  // Dark Red
        )
        
        /**
         * Creates an initial state for a specific battery style.
         */
        fun forStyle(style: BatteryStyle): CustomizeState {
            return CustomizeState(
                selectedStyle = style,
                previewConfig = style.defaultConfig,
                showEmojiToggle = style.defaultConfig.showEmoji,
                showPercentageToggle = style.defaultConfig.showPercentage,
                percentageFontSize = style.defaultConfig.percentageFontSizeDp,
                emojiSizeScale = style.defaultConfig.emojiSizeScale,
                percentageColor = style.defaultConfig.percentageColor
            )
        }
    }
}

/**
 * Events that can be triggered in the customization screen.
 * Follows the established MVI event pattern used throughout the app.
 */
sealed class CustomizeEvent {
    
    // Initialization events
    object LoadInitialData : CustomizeEvent()
    data class InitializeWithStyle(val style: BatteryStyle) : CustomizeEvent()
    
    // Style selection events
    data class SelectBatteryStyle(val style: BatteryStyle) : CustomizeEvent()
    data class SelectEmojiStyle(val style: BatteryStyle) : CustomizeEvent()
    
    // Global feature events
    data class ToggleGlobalEnabled(val enabled: Boolean) : CustomizeEvent()
    
    // Customization events
    data class ToggleShowEmoji(val show: Boolean) : CustomizeEvent()
    data class ToggleShowPercentage(val show: Boolean) : CustomizeEvent()
    data class UpdatePercentageFontSize(val size: Int) : CustomizeEvent()
    data class UpdateEmojiSizeScale(val scale: Float) : CustomizeEvent()
    data class UpdatePercentageColor(val color: Int) : CustomizeEvent()
    
    // Color picker events
    object ShowColorPicker : CustomizeEvent()
    object HideColorPicker : CustomizeEvent()
    data class SelectColor(val color: Int, val index: Int) : CustomizeEvent()
    
    // Preview events
    data class UpdatePreviewBatteryLevel(val level: Int) : CustomizeEvent()
    object RefreshPreview : CustomizeEvent()
    
    // Action events
    object ApplyCustomization : CustomizeEvent()
    object ResetToDefaults : CustomizeEvent()
    object NavigateBack : CustomizeEvent()
    object ClearNavigationState : CustomizeEvent()
    
    // System events
    object OnResume : CustomizeEvent()
    object OnPause : CustomizeEvent()
    object RetryLoad : CustomizeEvent()
    object ClearError : CustomizeEvent()
    
    // Validation events
    object ValidateForm : CustomizeEvent()
    data class ShowValidationError(val message: String) : CustomizeEvent()
}
