package com.tqhit.battery.one.features.emoji.presentation.customize.adapter

import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.ItemBatteryComponentBinding
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle

/**
 * DiffUtil callback for efficient battery component list updates.
 * Compares battery styles by ID and content to determine what changed.
 */
private class BatteryComponentDiffCallback : DiffUtil.ItemCallback<BatteryStyle>() {
    override fun areItemsTheSame(oldItem: BatteryStyle, newItem: BatteryStyle): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(oldItem: BatteryStyle, newItem: BatteryStyle): Boolean {
        return oldItem == newItem
    }
}

/**
 * Specialized RecyclerView adapter for displaying battery components in the customize screen.
 * This adapter is designed specifically for Phase 1 of the emoji carousel feature.
 * 
 * Key Features:
 * - Displays only the battery container image (batteryImageUrl) from BatteryStyle objects
 * - Uses ListAdapter with DiffUtil for efficient updates
 * - Implements selection state management for mix-and-match functionality
 * - Optimized for horizontal carousel layout
 * - Follows established patterns from BatteryStyleAdapter but specialized for component display
 */
class BatteryComponentAdapter(
    private val parentContext: Context,
    private val onBatteryComponentClick: (BatteryStyle) -> Unit,
    private val onPremiumUnlock: (BatteryStyle) -> Unit
) : ListAdapter<BatteryStyle, BatteryComponentViewHolder>(BatteryComponentDiffCallback()) {
    
    companion object {
        private const val TAG = "BatteryComponentAdapter"
    }
    
    private var selectedBatteryId: String? = null
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BatteryComponentViewHolder {
        Log.d(TAG, "BATTERY_COMPONENT_ADAPTER: Creating new ViewHolder")
        val binding = ItemBatteryComponentBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        Log.d(TAG, "BATTERY_COMPONENT_ADAPTER: ViewHolder created successfully")
        return BatteryComponentViewHolder(binding)
    }

    override fun onBindViewHolder(holder: BatteryComponentViewHolder, position: Int) {
        if (position >= itemCount) {
            Log.e(TAG, "BATTERY_COMPONENT_ADAPTER: Invalid position $position for items size $itemCount")
            return
        }

        val item = getItem(position)
        val isSelected = item.id == selectedBatteryId
        
        Log.d(TAG, "BATTERY_COMPONENT_ADAPTER: Binding battery component at position $position: name='${item.name}', id='${item.id}', selected=$isSelected")
        holder.bind(
            item,
            parentContext,
            isSelected,
            onBatteryComponentClick,
            onPremiumUnlock,
            position
        )
        Log.d(TAG, "BATTERY_COMPONENT_ADAPTER: Successfully bound battery component at position $position")
    }
    
    /**
     * Updates the selected battery component and refreshes the UI
     */
    fun updateSelection(selectedBatteryId: String?) {
        val oldSelectedId = this.selectedBatteryId
        this.selectedBatteryId = selectedBatteryId
        
        Log.d(TAG, "BATTERY_COMPONENT_ADAPTER: Selection updated from '$oldSelectedId' to '$selectedBatteryId'")
        
        // Refresh items that were previously selected or are now selected
        currentList.forEachIndexed { index, item ->
            if (item.id == oldSelectedId || item.id == selectedBatteryId) {
                notifyItemChanged(index)
            }
        }
    }
}

/**
 * ViewHolder for battery component items in the customize carousel.
 * Handles battery container image loading and selection state display.
 */
class BatteryComponentViewHolder(
    private val binding: ItemBatteryComponentBinding
) : RecyclerView.ViewHolder(binding.root) {
    
    companion object {
        private const val TAG = "BatteryComponentViewHolder"
    }
    
    fun bind(
        item: BatteryStyle,
        parentContext: Context,
        isSelected: Boolean,
        onBatteryComponentClick: (BatteryStyle) -> Unit,
        onPremiumUnlock: (BatteryStyle) -> Unit,
        position: Int
    ) {
        try {
            Log.d(TAG, "BATTERY_COMPONENT_ADAPTER: Binding position $position - ${item.name}, selected: $isSelected")
            
            // Start shimmer loading effect
            binding.shimmerLayout.startShimmer()
            binding.shimmerLayout.visibility = View.VISIBLE
            
            // Show/hide premium indicator
            binding.lockBtn.visibility = if (item.isPremium) View.VISIBLE else View.GONE
            
            // Update selection state
            updateSelectionState(isSelected)
            
            // Load battery container image only (not the combined style image)
            loadBatteryImage(item)
            
            // Set click listeners
            setupClickListeners(item, onBatteryComponentClick, onPremiumUnlock, position)
            
            Log.d(TAG, "BATTERY_COMPONENT_ADAPTER: Successfully bound battery component at position $position: ${item.name}, premium: ${item.isPremium}, selected: $isSelected")
            
        } catch (exception: Exception) {
            Log.e(TAG, "BATTERY_COMPONENT_ADAPTER: Error binding battery component at position $position", exception)
        }
    }
    
    private fun updateSelectionState(isSelected: Boolean) {
        // Update selection border/background
        if (isSelected) {
            binding.componentContainer.setBackgroundResource(R.drawable.bg_component_selected)
            binding.selectionIndicator.visibility = View.VISIBLE
        } else {
            binding.componentContainer.setBackgroundResource(R.drawable.bg_component_default)
            binding.selectionIndicator.visibility = View.GONE
        }
    }
    
    private fun loadBatteryImage(item: BatteryStyle) {
        val imageUrl = item.batteryImageUrl
        
        Log.d(TAG, "BATTERY_COMPONENT_ADAPTER: Loading battery image: $imageUrl")
        
        val requestOptions = RequestOptions()
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .placeholder(R.drawable.placeholder_battery_component)
            .error(R.drawable.error_battery_component)
        
        Glide.with(binding.root.context)
            .load(imageUrl)
            .apply(requestOptions)
            .into(binding.batteryImage)
            .also {
                // Stop shimmer when image loads
                binding.shimmerLayout.stopShimmer()
                binding.shimmerLayout.visibility = View.GONE
            }
    }
    
    private fun setupClickListeners(
        item: BatteryStyle,
        onBatteryComponentClick: (BatteryStyle) -> Unit,
        onPremiumUnlock: (BatteryStyle) -> Unit,
        position: Int
    ) {
        binding.componentContainer.setOnClickListener {
            Log.d(TAG, "BATTERY_COMPONENT_ADAPTER: Battery component clicked at position $position: ${item.name}")
            
            if (item.isPremium) {
                Log.d(TAG, "BATTERY_COMPONENT_ADAPTER: Premium battery component clicked, triggering unlock flow")
                onPremiumUnlock(item)
            } else {
                Log.d(TAG, "BATTERY_COMPONENT_ADAPTER: Free battery component clicked, triggering selection")
                onBatteryComponentClick(item)
            }
        }
        
        binding.lockBtn.setOnClickListener {
            Log.d(TAG, "BATTERY_COMPONENT_ADAPTER: Premium lock button clicked for: ${item.name}")
            onPremiumUnlock(item)
        }
    }
}
