# Product Requirements Document: Emoji "Customize" Page
**Version:** 2.2
**Date:** July 1, 2025
**Status:** In Development (Phase 2 - Data Binding & State Logic)

## 1. Product Design Specification

### Overview & Goal
The "Customize" page is the creative hub for the Emoji Battery feature. After selecting a style from the gallery, users land here to fine-tune its appearance. The primary goal is to provide an intuitive and highly visual editing experience with a real-time preview, empowering users to create a personalized status bar icon that perfectly matches their style, including the ability to **mix and match** battery containers and emoji characters.

### Screen Layout & Components

#### **1.1. Header Bar**
*   **Component:** `Toolbar`
*   **Elements:**
    *   **Back Navigation:** A standard back arrow icon on the left to return to the gallery.
    *   **Title:** Centered text displaying "Customize".

#### **1.2. Global Toggle**
*   **Component:** A container with a rounded rectangle shape.
*   **Content:**
    *   **Label:** "Enable or disable the emoji battery".
    *   **Control:** A standard toggle switch (`SwitchMaterial`) for the global on/off state of the feature.

#### **1.3. Live Preview Area**
*   **Component:** A large, prominent `CardView` with rounded corners.
*   **Content:** A real-time, large-scale preview of the emoji battery.
    *   **Percentage Text:** Displays a sample battery level (e.g., "50%"). Its font size, scale, and visibility must update in real-time.
    *   **Battery Image:** An `ImageView` (`preview_battery`) that displays the currently selected battery container image.
    *   **Emoji Image:** An `ImageView` (`preview_emoji`) that displays the currently selected emoji/character image, overlaid on the battery image. Its scale and visibility must update in real-time.

#### **1.4. Style Selection ("Mix-and-Match")**
This section is the core of the customization, allowing users to combine different components.

*   **"Battery" Section:**
    *   **Component:** A horizontal `RecyclerView`.
    *   **Content:** A scrollable list of different **battery container** styles, sourced from items in the same category.
    *   **Interaction:** The currently selected container is highlighted. Tapping another updates the selection and the live preview's battery image.
    *   **Monetization:** Premium styles are marked with a diamond icon.

*   **"Emoji" Section:**
    *   **Component:** A horizontal `RecyclerView`.
    *   **Content:** A scrollable list of different **emoji/character** styles, sourced from items in the same category.
    *   **Interaction:** The selected emoji is highlighted and updates the live preview's emoji image.
    *   **Monetization:** Premium emojis are marked with a diamond icon.
    *   **"More >" Link:** (Future enhancement) A text link to navigate to a full-screen emoji gallery.

#### **1.5. Customization Controls**
*   **`Show Emoji` Toggle:** A toggle switch to show/hide the emoji in the preview.
*   **`Show Percentage` Toggle:** A toggle switch to show/hide the percentage text.
*   **`Percentage` Slider (Font Size):** A `SeekBar` controlling the font size of the percentage text (Range: 5dp to 40dp).
*   **`Emoji Battery` Slider (Emoji Scale):** A `SeekBar` controlling the scale of the emoji image (Range: 0.5x to 2.0x).

#### **1.6. Call to Action**
*   **Component:** A full-width `Button`.
*   **Label:** "Apply".
*   **Action:** Saves the user's final mixed-and-matched configuration and enables the overlay service.

#### **1.7. Ad Banner**
*   **Component:** A standard banner ad container at the bottom.

---

## 2. Codebase Analysis & Implementation Plan

The project has an excellent foundation. The following plan details the current status and the precise steps needed to complete the feature.

### Current Implementation Status

**✅ What's Already Implemented:**

1.  **Core Architecture:** The `EmojiCustomizeActivity`, `CustomizeViewModel`, and `CustomizeState` provide a solid MVI foundation.
2.  **Navigation:** Navigation from the gallery to the activity, including passing the initial `BatteryStyle`, is working perfectly.
3.  **Persistence Layer:** `CustomizationRepository` and its use cases are fully implemented and ready to save the final configuration.
4.  **UI Controls:** The toggles and sliders for visibility, font size, and scale are implemented and correctly wired to the ViewModel.
5.  **Live Preview:** The preview area correctly loads the initial combined style image using Glide. The percentage text and emoji visibility/scale update in real-time.
6.  **Data Fetching:** The `CustomizeViewModel`'s `loadStyleAlternatives` method correctly fetches the **complete list of all styles from the same category**. This provides the necessary data source for both carousels.

**🟡 What's Missing / The Gap:**

The primary gap is connecting the fetched `List<BatteryStyle>` data to the "Battery" and "Emoji" carousels and implementing the "mix-and-match" state logic. The current UI adapters and state management are not yet set up for this composite selection.

### Implementation Plan: From Gaps to Completion

#### **Phase 1: Implement Component-Specific Display (Next Steps)**

**Objective:** Populate the "Battery" and "Emoji" carousels with the correct images from the fetched data.

*   **Task 1.1: Create Specialized `RecyclerView` Adapters.**
    *   **Action:** Create two new, simple `ListAdapter` classes. The existing `BatteryStyleAdapter` is designed for gallery thumbnails and is not suitable.
        *   `BatteryComponentAdapter.kt`: Its `onBindViewHolder` will receive a `BatteryStyle` object but will **only load the `batteryImageUrl`** into its `ImageView`.
        *   `EmojiComponentAdapter.kt`: Its `onBindViewHolder` will receive a `BatteryStyle` object but will **only load the `emojiImageUrl`** into its `ImageView`.
    *   **Rationale:** This separates the display logic, allowing you to reuse the same data source (`List<BatteryStyle>`) for both carousels while showing different parts of the data.

*   **Task 1.2: Update ViewModel and Activity for New Adapters.**
    *   **File:** `CustomizeViewModel.kt`
    *   **Action:** The current `availableBatteryStyles` and `availableEmojiStyles` are populated with placeholder/incorrect data. **Refactor the `loadStyleAlternatives` method so that both properties are populated from the single, correct list of `emojiItems` fetched from the same category.**
    *   **File:** `EmojiCustomizeActivity.kt`
    *   **Action:** In `setupRecyclerViews`, create and assign instances of your new `BatteryComponentAdapter` and `EmojiComponentAdapter`.
    *   **Action:** In `updateStyleSelections`, submit the `state.availableBatteryStyles` and `state.availableEmojiStyles` lists to their respective new adapters.

#### **Phase 2: Implement "Mix-and-Match" State Logic**

**Objective:** Allow the user to select components independently and see the result in the live preview.

*   **Task 2.1: Refactor `CustomizeState` for Composite Selection.**
    *   **File:** `CustomizeState.kt`
    *   **Action:** The current state `selectedStyle: BatteryStyle?` cannot handle a mixed selection. Refactor it to hold the components separately.
        ```kotlin
        data class CustomizeState(
            // ... other properties
            // DEPRECATE: val selectedStyle: BatteryStyle? = null
            val baseStyle: BatteryStyle?, // The initial style from the gallery
            val selectedBattery: BatteryStyle?, // The user's chosen battery component
            val selectedEmoji: BatteryStyle?, // The user's chosen emoji component
        )
        ```
    *   **Initialization:** When the ViewModel is initialized, set `baseStyle`, `selectedBattery`, and `selectedEmoji` to the style passed from the gallery.

*   **Task 2.2: Update ViewModel Events and Handlers.**
    *   **File:** `CustomizeViewModel.kt`
    *   **Action:** `SelectBatteryStyle` and `SelectEmojiStyle` events are already defined. **Modify their handlers** to update *only* the `selectedBattery` or `selectedEmoji` properties in the `CustomizeState`. The other component should remain unchanged.

*   **Task 2.3: Update UI to React to Composite State.**
    *   **File:** `EmojiCustomizeActivity.kt`
    *   **Action:** Modify `loadPreviewImages`. Instead of loading from a single `style` object, it should now load from the composite state:
        *   The battery `ImageView` should load `state.selectedBattery?.batteryImageUrl`.
        *   The emoji `ImageView` should load `state.selectedEmoji?.emojiImageUrl`.
    *   **Important:** Handle null cases gracefully during the initial load.

#### **Phase 3: Finalize and Polish**

**Objective:** Complete the remaining features and ensure a polished user experience.

*   **Task 3.1: Implement the "Apply" Logic.**
    *   **File:** `CustomizeViewModel.kt`
    *   **Action:** When `ApplyCustomization` is triggered, construct a new, final `CustomizationConfig`. The `selectedStyleId` you save should uniquely represent the user's combination (e.g., `"${state.selectedBattery.id}_${state.selectedEmoji.id}"`). Save this config using the `saveCustomizationUseCase`.

*   **Task 3.2: Implement Premium Flow and UI Polish.**
    *   **Action:**
        *   Implement the premium unlock dialog when a premium item is tapped in the carousels.
        *   Add a UI component for the color picker (the state property already exists).
        *   Connect the banner ad using `ApplovinBannerAdManager`.

### Optimization Opportunities

1.  **Data Fetching Efficiency:**
    *   The `CustomizeViewModel` currently re-fetches the list of items for the selected category.
    *   **Optimization:** Pass the `List<EmojiItem>` (or `List<BatteryStyle>`) for the selected category from `EmojiBatteryFragment` to `EmojiCustomizeActivity` via the `Intent`. This will avoid a redundant network call and make the customize screen load instantly.

2.  **Image Loading Performance:**
    *   The carousel and preview images can be large.
    *   **Optimization:** Use Glide's `.thumbnail()` API to first load the low-resolution `galleryThumbnailUrl` almost instantly, and then have Glide replace it with the high-resolution `batteryImageUrl` or `emojiImageUrl` once it's downloaded. This drastically improves perceived performance.
    ```kotlin
    // Example for a carousel adapter
    Glide.with(this)
        .load(style.batteryImageUrl) // High-res URL
        .thumbnail(Glide.with(this).load(style.galleryThumbnailUrl)) // Low-res thumbnail
        .into(imageView)
    ```