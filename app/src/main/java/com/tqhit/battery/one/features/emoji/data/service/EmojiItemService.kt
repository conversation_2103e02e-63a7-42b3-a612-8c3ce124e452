package com.tqhit.battery.one.features.emoji.data.service

import com.google.gson.Gson
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.features.emoji.domain.model.EmojiItem
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service responsible for fetching and parsing emoji items from Firebase Remote Config.
 * Fetches items by category ID and provides fallback to default items.
 * 
 * This service follows the established patterns in the app:
 * - Uses Firebase Remote Config for dynamic content
 * - Provides comprehensive error handling with detailed logging
 * - Follows SOLID principles and clean architecture
 * - Uses coroutines for asynchronous operations
 */
@Singleton
class EmojiItemService @Inject constructor(
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    private val gson: Gson
) {
    companion object {
        private const val TAG = "EmojiItemService"
    }
    
    /**
     * Fetches emoji items for a specific category from Firebase Remote Config.
     * Returns only items with status=true, sorted by priority.
     *
     * The Firebase Remote Config SDK automatically handles fallback to remote_config_defaults.xml
     * when network data is unavailable, so no custom fallback logic is needed.
     *
     * @param categoryId The category ID to fetch items for (e.g., "animal_category")
     * @return List of valid emoji items for the category, sorted by priority
     */
    suspend fun getEmojiItemsByCategory(categoryId: String): List<EmojiItem> = withContext(Dispatchers.IO) {
        try {
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Fetching emoji items for category: $categoryId")

            val jsonString = remoteConfigHelper.getString(categoryId)
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Retrieved JSON string length: ${jsonString.length} for category: $categoryId")

            val items = parseEmojiItems(jsonString, categoryId)
            val validItems = filterAndSortItems(items, categoryId)

            BatteryLogger.d(TAG, "REMOTE_CONFIG: Successfully loaded ${validItems.size} valid items for category: $categoryId")
            return@withContext validItems

        } catch (e: Exception) {
            BatteryLogger.e(TAG, "REMOTE_CONFIG: Error parsing emoji items JSON for category: $categoryId", e)
            return@withContext emptyList()
        }
    }
    
    /**
     * Parses JSON string into list of EmojiItem objects.
     * 
     * @param jsonString The JSON string from remote config
     * @param categoryId The category ID for logging context
     * @return List of parsed emoji items, or empty list if parsing fails
     */
    private fun parseEmojiItems(jsonString: String, categoryId: String): List<EmojiItem> {
        return try {
            val items = gson.fromJson(jsonString, Array<EmojiItem>::class.java).toList()
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Successfully parsed ${items.size} emoji items for category: $categoryId")
            items
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "REMOTE_CONFIG: Error parsing emoji items JSON for category: $categoryId", e)
            emptyList()
        }
    }
    
    /**
     * Filters and sorts emoji items by validity and status.
     * Only returns items with status=true and valid data, sorted by priority.
     * 
     * @param items The list of items to filter and sort
     * @param categoryId The category ID for logging context
     * @return List of valid items sorted by priority
     */
    private fun filterAndSortItems(items: List<EmojiItem>, categoryId: String): List<EmojiItem> {
        val validItems = items.filter { item ->
            val isValid = item.isValid() && item.status
            if (!isValid) {
                BatteryLogger.w(TAG, "REMOTE_CONFIG: Filtering out invalid/disabled item: ${item.id} in category: $categoryId")
            }
            isValid
        }.sortedBy { it.priority }
        
        BatteryLogger.d(TAG, "REMOTE_CONFIG: Filtered to ${validItems.size} valid items for category: $categoryId")
        
        // Log item details for debugging
        validItems.forEachIndexed { index, item ->
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Item $index in $categoryId: ${item.name} (id=${item.id}, priority=${item.priority}, isPremium=${item.isPremium})")
        }
        
        return validItems
    }
    

    
    /**
     * Gets all available category IDs that have emoji items in remote config.
     * This can be used to validate which categories have content available.
     * 
     * @return List of category IDs that should have emoji items
     */
    fun getAvailableCategoryIds(): List<String> {
        return listOf(
            "hot_category",
            "brainrot_category", 
            "character_category",
            "heart_category",
            "cute_category",
            "sticker3d_category",
            "emotion_category",
            "animal_category",
            "food_category",
            "other_category"
        )
    }
    
    /**
     * Checks if a category ID is supported by the remote config.
     * 
     * @param categoryId The category ID to check
     * @return true if the category is supported, false otherwise
     */
    fun isCategorySupportedInRemoteConfig(categoryId: String): Boolean {
        return getAvailableCategoryIds().contains(categoryId)
    }
}
