app/src/main/java/com/tqhit/battery/one/features/emoji

Based on the successful implementation of the emoji battery overlay with solid background and custom status bar icons, please refactor the code to improve maintainability and reduce complexity. Focus on these specific areas:

## 1. EmojiBatteryView.kt - Icon Drawing Consolidation ✅ COMPLETED

**PRIORITY: HIGH** - Eliminate code duplication in icon drawing methods.

**Current Issue:** The methods `drawWifiIcon()`, `drawCellularIcon()`, and `drawRingSilentIcon()` in EmojiBatteryView.kt contain nearly identical logic (load drawable, set bounds, draw to canvas).

**Required Action:** Create a single private helper method `drawStatusIcon()` that:
- Takes canvas, position coordinates, drawable resource ID as parameters
- Handles drawable loading, bounds setting, and canvas drawing
- Returns the icon width for layout calculations
- Uses consistent icon sizing (16dp) and pink color scheme (#FF6B9D)

**Expected Outcome:** Replace the three separate icon drawing methods with calls to the consolidated helper, reducing code duplication by ~60 lines.

**✅ IMPLEMENTATION COMPLETED:**
- Created consolidated `drawStatusIcon()` helper method with all required parameters
- Replaced `drawWifiIcon()`, `drawCellularIcon()`, and `drawSilentModeIcon()` with calls to helper
- Maintained all existing functionality including logging and theming
- Reduced code duplication by approximately 60 lines
- Improved maintainability and consistency across status icon drawing

## 2. EmojiBatteryAccessibilityService.kt - Touch Handling Cleanup ✅ COMPLETED

**PRIORITY: HIGH** - Remove redundant touch handling logic.

**Current Issue:** Both the service and EmojiBatteryView handle touch events, creating duplicate and conflicting touch logic.

**Required Action:** 
- Remove all touch handling from EmojiBatteryAccessibilityService.kt including:
  - `emojiInitialTouchY`, `emojiInitialTouchX`, `emojiTouchStartTime` properties
  - `handleEmojiTouchEvent()` method
  - Touch-related logging in the service
- Keep only the touch handling in EmojiBatteryView.kt using its existing GestureDetector
- Ensure the overlay view can receive touch events (verify FLAG_NOT_TOUCHABLE is not set)

**Expected Outcome:** Single source of truth for touch handling, eliminating ~50 lines of redundant code.

**✅ IMPLEMENTATION COMPLETED:**
- Removed `emojiInitialTouchY`, `emojiInitialTouchX`, `emojiTouchStartTime` properties from service
- Removed `handleEmojiTouchEvent()` method and associated touch logic (~40 lines)
- Removed service-level `setOnTouchListener` from overlay view setup
- Verified window layout parameters allow touch events (FLAG_NOT_TOUCHABLE not set)
- Touch handling now exclusively managed by EmojiBatteryView's internal GestureDetector
- Single source of truth for gesture detection achieved

## 3. Timer to Coroutines Modernization ✅ COMPLETED

**PRIORITY: MEDIUM** - Replace java.util.Timer with coroutines for status bar updates.

**Current Issue:** Using `Timer` for 30-second status bar updates is inconsistent with the coroutine-based architecture.

**Required Action:**
- Replace `statusBarUpdateTimer: Timer?` with `statusBarUpdateJob: Job?`
- Create `startStatusBarUpdater()` and `stopStatusBarUpdater()` methods using coroutines
- Use `serviceScope.launch` with `delay(30_000L)` for 30-second intervals
- Ensure updates run on main thread using `withContext(Dispatchers.Main)`
- Fix the current threading issue where timer updates cause ViewRootImpl exceptions

**Expected Outcome:** Consistent coroutine usage and elimination of threading errors.

**✅ IMPLEMENTATION COMPLETED:**
- Replaced `statusBarUpdateTimer: Timer?` with `statusBarUpdateJob: Job?`
- Created `startStatusBarUpdater()` and `stopStatusBarUpdater()` methods using coroutines
- Implemented `serviceScope.launch` with `while(isActive)` loop and `delay(30_000L)`
- Added `withContext(Dispatchers.Main)` to ensure UI updates run on main thread
- Removed unused Timer imports (`java.util.*`, `kotlin.concurrent.schedule`)
- Added required coroutine imports (`delay`, `isActive`, `withContext`)
- Threading issues with ViewRootImpl should now be resolved

## 4. Resource Externalization

**PRIORITY: LOW** - Move hardcoded values to XML resources.

**Current Issue:** Colors like "#FF6B9D" and dimensions are hardcoded in Kotlin files.

**Required Action:**
- Move all colors to `res/values/colors.xml` (emoji_overlay_pink, etc.)
- Move dimensions to `res/values/dimens.xml` (icon_size_16dp, overlay_padding, etc.)
- Update Kotlin code to reference these resources using `ContextCompat.getColor()` and dimension resources

**Expected Outcome:** Better maintainability and theme support.

## Context and Constraints:
- Maintain all existing functionality (solid background, custom status bar icons, emoji display)
- Preserve the comprehensive logging system for debugging
- Keep the pink color scheme (#FF6B9D) and current visual appearance
- Ensure the overlay continues to work with the accessibility service architecture
- Test changes using the existing ADB commands: `adb shell am start -n com.fc.p.tj.charginganimation.batterycharging.chargeeffect/com.tqhit.battery.one.activity.splash.SplashActivity`

## Success Criteria:
- Reduced code duplication (target: ~100+ lines removed)
- Single touch handling implementation
- No threading exceptions in logs
- All existing visual and functional behavior preserved
- Cleaner, more maintainable codebase structure