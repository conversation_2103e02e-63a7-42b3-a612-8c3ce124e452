### Overall Layout: Main Container

This is the main component that holds all the other elements.

*   **Shape:** A horizontal bar with prominent rounded corners.
*   **Background:** Solid white (`#FFFFFF`).
*   **Layout:** It acts as a single row containing two main groups of items. One group is aligned to the start (left) and the other is aligned to the end (right), with empty space between them. This can be achieved with a `ConstraintLayout` or a `RelativeLayout`.
*   **Padding:** There is internal padding on the left and right sides of the container, ensuring the content doesn't touch the edges.

---

### Left-Aligned Group

This group contains items aligned to the start of the main container. They are arranged horizontally with consistent spacing between them.

1.  **Time (TextView)**
    *   **Content:** Text displaying the time in HH:mm format (e.g., "18:09").
    *   **Text Color:** Solid black.
    *   **Font:** A standard sans-serif font with a medium or bold weight.

2.  **Silent Mode (ImageView)**
    *   **Icon:** A solid black bell icon with a diagonal slash through it, indicating silent or "do not disturb" mode is active.
    *   **Position:** To the right of the time, with a small margin.

3.  **Emoji (TextView or ImageView)**
    *   **Icon:** The "Face with Tears of Joy" emoji (😂).
    *   **Position:** To the right of the silent mode icon, with a similar small margin.

---

### Right-Aligned Group

This group contains items aligned to the end of the main container. They are also arranged horizontally.

1.  **Cellular Signal (ImageView)**
    *   **Icon:** A cellular signal strength icon with four vertical bars of increasing height from left to right. In the image, three out of four bars are filled.
    *   **Color:** Solid black.

2.  **Wi-Fi Signal (ImageView)**
    *   **Icon:** A standard Wi-Fi icon with three curved arcs above a dot.
    *   **Color:** Solid black.
    *   **Position:** To the left of the cellular icon, with a small margin.

3.  **Battery Indicator (Composite View / FrameLayout)**
    *   This is a layered component, best built using a `FrameLayout` or `ConstraintLayout`.
    *   **Position:** To the left of the Wi-Fi icon, with a small margin.
    *   **Layer 1: Battery Outline (ImageView)**
        *   **Icon:** A standard horizontal battery icon shape (a rectangle with a small nub on the right end).
        *   **Style:** Black outline, transparent fill.
    *   **Layer 2: Battery Level (View)**
        *   **Shape:** A rectangle placed inside the battery outline. Its width should be a percentage of the container's width (e.g., 63% width).
        *   **Color:** A solid pink/magenta color.
        *   **Alignment:** Aligned to the left side of the battery outline.
    *   **Layer 3: Heart Icon (ImageView)**
        *   **Icon:** A heart shape.
        *   **Color:** A semi-transparent pink/magenta color, slightly darker than the battery level fill.
        *   **Alignment:** Centered horizontally and vertically on top of the battery icon.
    *   **Layer 4: Percentage Text (TextView)**
        *   **Content:** The battery percentage as text (e.g., "63%").
        *   **Text Color:** White.
        *   **Font:** Sans-serif, smaller font size than the main clock time.
        *   **Alignment:** Centered perfectly inside the heart icon.