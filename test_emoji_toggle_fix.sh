#!/bin/bash

# Test script for verifying the emoji toggle permission fix
# This script monitors logs specifically for toggle events and permission handling

set -e

echo "======================================"
echo "🔧 Emoji Toggle Fix Verification Test"
echo "======================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_step() {
    echo -e "${BLUE}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

print_test() {
    echo -e "${PURPLE}🧪 $1${NC}"
}

# Check if device is connected
print_step "Checking ADB connection..."
if ! adb devices | grep -q "device$"; then
    print_error "No Android device found. Please connect a device and enable USB debugging."
    exit 1
fi
print_success "Device connected"

# Clear logcat buffer
print_step "Clearing logcat buffer..."
adb logcat -c
print_success "Logcat cleared"

# Start the app if not already running
print_step "Ensuring app is running..."
PACKAGE_NAME="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
MAIN_ACTIVITY="com.tqhit.battery.one.activity.splash.SplashActivity"
adb shell am start -n "$PACKAGE_NAME/$MAIN_ACTIVITY" > /dev/null 2>&1
print_success "App started"

# Wait for app to initialize
print_info "Waiting for app initialization (5 seconds)..."
sleep 5

# Create log file
LOG_FILE="emoji_toggle_fix_test_$(date +%Y%m%d_%H%M%S).txt"

print_step "Starting emoji toggle monitoring..."
echo
echo "======================================"
echo "🔧 EMOJI TOGGLE MONITORING"
echo "======================================"
echo

print_test "TEST INSTRUCTIONS:"
print_warning "1. Navigate to the emoji battery gallery screen"
print_warning "2. Try toggling the global emoji battery switch ON"
print_warning "3. Watch for permission dialog to appear"
print_warning "4. If dialog appears, click 'OK' to open accessibility settings"
print_warning "5. Grant accessibility permission if needed"
print_warning "6. Return to the app and verify toggle works"
print_info "This test will run for 60 seconds to capture the complete flow"
print_info "Press Ctrl+C to stop early if needed"

echo
print_step "Monitoring toggle events and permissions..."

# Monitor specific logs for toggle functionality
timeout 60 adb logcat -s \
    "BatteryGalleryVM:*" \
    "EmojiBatteryFragment:*" \
    "EmojiOverlayPermissionManager:*" \
    "EmojiAccessibilityServiceManager:*" \
    "EmojiBatteryAccessibilityService:*" \
    | tee "$LOG_FILE" | while IFS= read -r line; do
    
    # Color-code different types of log messages
    if [[ $line == *"ToggleGlobalFeature"* ]]; then
        echo -e "${GREEN}🔄 TOGGLE: $line${NC}"
    elif [[ $line == *"EMOJI_PERMISSION"* ]]; then
        echo -e "${PURPLE}🔐 PERMISSION: $line${NC}"
    elif [[ $line == *"shouldRequestPermissions"* ]]; then
        echo -e "${YELLOW}📋 PERMISSION REQUEST: $line${NC}"
    elif [[ $line == *"handlePermissionRequest"* ]]; then
        echo -e "${CYAN}🔧 PERMISSION HANDLER: $line${NC}"
    elif [[ $line == *"EMOJI_DIALOG_TAG"* ]]; then
        echo -e "${BLUE}💬 DIALOG: $line${NC}"
    elif [[ $line == *"EMOJI_SERVICE"* ]]; then
        echo -e "${GREEN}⚙️  SERVICE: $line${NC}"
    elif [[ $line == *"ERROR"* ]]; then
        echo -e "${RED}❌ ERROR: $line${NC}"
    else
        echo "$line"
    fi
done

echo
print_step "Monitoring completed. Analyzing results..."

# Analyze the results
echo
echo "======================================"
echo "📊 TEST ANALYSIS"
echo "======================================"

if [ -f "$LOG_FILE" ]; then
    # Count key events
    TOGGLE_EVENTS=$(grep -c "ToggleGlobalFeature" "$LOG_FILE" 2>/dev/null || echo "0")
    PERMISSION_REQUESTS=$(grep -c "shouldRequestPermissions.*true" "$LOG_FILE" 2>/dev/null || echo "0")
    PERMISSION_HANDLERS=$(grep -c "handlePermissionRequest" "$LOG_FILE" 2>/dev/null || echo "0")
    PERMISSION_DIALOGS=$(grep -c "EMOJI_DIALOG_TAG" "$LOG_FILE" 2>/dev/null || echo "0")
    SERVICE_EVENTS=$(grep -c "EMOJI_SERVICE" "$LOG_FILE" 2>/dev/null || echo "0")
    ERROR_COUNT=$(grep -c "ERROR" "$LOG_FILE" 2>/dev/null || echo "0")
    
    print_info "=== TOGGLE ANALYSIS RESULTS ==="
    echo "🔄 Toggle events detected: $TOGGLE_EVENTS"
    echo "📋 Permission requests triggered: $PERMISSION_REQUESTS"
    echo "🔧 Permission handlers called: $PERMISSION_HANDLERS"
    echo "💬 Permission dialogs shown: $PERMISSION_DIALOGS"
    echo "⚙️  Service events: $SERVICE_EVENTS"
    echo "❌ Errors found: $ERROR_COUNT"
    
    echo
    print_info "=== FIX VERIFICATION ==="
    
    # Check if the fix is working
    if [ "$TOGGLE_EVENTS" -gt 0 ]; then
        print_success "✅ Toggle events detected - users are interacting with the switch"
        
        if [ "$PERMISSION_REQUESTS" -gt 0 ]; then
            print_success "✅ Permission requests are being triggered"
            
            if [ "$PERMISSION_HANDLERS" -gt 0 ]; then
                print_success "✅ Permission handlers are being called - FIX IS WORKING!"
                
                if [ "$PERMISSION_DIALOGS" -gt 0 ]; then
                    print_success "✅ Permission dialogs are being shown to users"
                else
                    print_warning "⚠️ No permission dialogs detected - check if permissions are already granted"
                fi
            else
                print_error "❌ Permission handlers NOT called - fragment may not be observing shouldRequestPermissions"
            fi
        else
            print_warning "⚠️ No permission requests detected - permissions may already be granted"
        fi
    else
        print_warning "⚠️ No toggle events detected - user may not have interacted with the switch"
    fi
    
    # Check for errors
    if [ "$ERROR_COUNT" -gt 0 ]; then
        print_warning "⚠️ $ERROR_COUNT errors found - check logs for details"
        echo "Recent errors:"
        grep "ERROR" "$LOG_FILE" | tail -3
    else
        print_success "✅ No errors found in logs"
    fi
    
    echo
    print_info "=== SAMPLE LOG ENTRIES ==="
    echo "Recent toggle events:"
    grep "ToggleGlobalFeature\|shouldRequestPermissions\|handlePermissionRequest" "$LOG_FILE" | tail -5
    
else
    print_error "❌ Log file not found: $LOG_FILE"
fi

echo
echo "======================================"
echo "📄 TEST SUMMARY"
echo "======================================"

print_success "Emoji toggle fix verification completed!"
print_info "Log file saved: $LOG_FILE"

echo
print_info "=== EXPECTED BEHAVIOR ==="
echo "1. When toggle is turned ON and permissions are missing:"
echo "   - shouldRequestPermissions should be set to true"
echo "   - handlePermissionRequest should be called"
echo "   - Permission explanation dialog should appear"
echo "   - User should be navigated to accessibility settings"
echo
echo "2. When permissions are granted:"
echo "   - Toggle should remain ON"
echo "   - Emoji overlay service should start"
echo "   - Overlay should appear on screen"

echo
print_info "=== TROUBLESHOOTING ==="
echo "- If no toggle events: Make sure to toggle the global switch in the app"
echo "- If no permission requests: Permissions may already be granted"
echo "- If permission handlers not called: The fix may need adjustment"
echo "- If dialogs not shown: Check dialog creation logic"

print_success "Test completed! Check $LOG_FILE for detailed logs."
echo "======================================" 