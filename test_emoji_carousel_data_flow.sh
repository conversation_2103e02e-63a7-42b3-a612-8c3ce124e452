#!/bin/bash

# Emoji Carousel Data Flow Test Script
# This script specifically tests the data loading flow for emoji carousels
# with comprehensive logging to identify where the flow breaks

set -e

echo "=========================================="
echo "EMOJI CAROUSEL DATA FLOW TEST"
echo "=========================================="
echo "Test started at: $(date)"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
PACKAGE_NAME="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
MAIN_ACTIVITY="com.tqhit.battery.one.activity.splash.SplashActivity"
CUSTOMIZE_ACTIVITY="com.tqhit.battery.one.features.emoji.presentation.customize.EmojiCustomizeActivity"

# Check if device is connected
echo -e "${BLUE}[INFO]${NC} Checking device connection..."
if ! adb devices | grep -q "device$"; then
    echo -e "${RED}[ERROR]${NC} No Android device connected!"
    exit 1
fi
echo -e "${GREEN}[SUCCESS]${NC} Device connected"
echo ""

# Function to wait and check logs
wait_and_check_logs() {
    local wait_time=$1
    local search_pattern="$2"
    local description="$3"
    
    echo -e "${BLUE}[INFO]${NC} ${description}"
    sleep $wait_time
    
    echo -e "${YELLOW}[LOGS]${NC} Checking logs for: ${search_pattern}"
    adb logcat -d | grep -E "$search_pattern" | tail -10
    echo ""
}

# Function to check specific log patterns
check_log_pattern() {
    local pattern="$1"
    local description="$2"
    
    echo -e "${BLUE}[CHECK]${NC} ${description}"
    local count=$(adb logcat -d | grep -E "$pattern" | wc -l)
    if [ $count -gt 0 ]; then
        echo -e "${GREEN}[FOUND]${NC} Found $count matching log entries"
        adb logcat -d | grep -E "$pattern" | tail -5
    else
        echo -e "${RED}[NOT FOUND]${NC} No matching log entries found"
    fi
    echo ""
}

# Clear logcat buffer
echo -e "${BLUE}[INFO]${NC} Clearing logcat buffer..."
adb logcat -c
echo ""

# Kill and restart the app
echo -e "${BLUE}[INFO]${NC} Killing existing app instances..."
adb shell am force-stop $PACKAGE_NAME
sleep 2

# Start the app
echo -e "${BLUE}[INFO]${NC} Starting the app..."
adb shell am start -n $PACKAGE_NAME/$MAIN_ACTIVITY
wait_and_check_logs 3 "(Battery|Emoji|Splash)" "App startup"

# Navigate through the app to reach emoji gallery
echo -e "${BLUE}[INFO]${NC} Navigating to emoji gallery..."
# Try to tap on emoji/battery section (approximate coordinates)
adb shell input tap 540 1200
wait_and_check_logs 2 "(Emoji|Gallery|Fragment)" "Emoji gallery navigation"

# Select an emoji style to trigger customize activity
echo -e "${BLUE}[INFO]${NC} Selecting an emoji style..."
adb shell input tap 200 800
wait_and_check_logs 3 "(EmojiCustomizeActivity|INITIALIZATION)" "Emoji style selection"

# Check if customize activity launched
check_log_pattern "EmojiCustomizeActivity created" "Customize activity creation"

# Check initialization flow
check_log_pattern "INITIALIZATION START" "ViewModel initialization start"
check_log_pattern "LOAD_STYLE_ALTERNATIVES START" "Style alternatives loading start"

# Check service call
check_log_pattern "Calling emojiItemService.getEmojiItemsByCategory" "Service call"
check_log_pattern "SERVICE RESPONSE" "Service response"

# Check data conversion
check_log_pattern "Converting emoji items to battery styles" "Data conversion"
check_log_pattern "STATE UPDATE COMPLETE" "State update completion"

# Check UI updates
check_log_pattern "UPDATE_STYLE_SELECTIONS START" "UI update start"
check_log_pattern "SUBMITTING BATTERY STYLES" "Battery styles submission"
check_log_pattern "SUBMITTING EMOJI STYLES" "Emoji styles submission"

# Check adapter logs
check_log_pattern "BatteryComponentAdapter.*Creating new" "Battery adapter creation"
check_log_pattern "EmojiComponentAdapter.*Creating new" "Emoji adapter creation"
check_log_pattern "EMOJI_CUSTOMIZE_ADAPTER.*bound" "Adapter binding"

# Check for errors
echo -e "${RED}[ERROR CHECK]${NC} Looking for errors in the flow..."
check_log_pattern "LOAD_STYLE_ALTERNATIVES ERROR" "Style alternatives loading errors"
check_log_pattern "Error.*emoji" "General emoji-related errors"
check_log_pattern "Exception.*emoji" "Emoji-related exceptions"

# Final summary
echo "=========================================="
echo "DATA FLOW ANALYSIS SUMMARY"
echo "=========================================="

# Count key events
INIT_COUNT=$(adb logcat -d | grep -c "INITIALIZATION START" || echo "0")
LOAD_COUNT=$(adb logcat -d | grep -c "LOAD_STYLE_ALTERNATIVES START" || echo "0")
SERVICE_COUNT=$(adb logcat -d | grep -c "SERVICE RESPONSE" || echo "0")
UPDATE_COUNT=$(adb logcat -d | grep -c "UPDATE_STYLE_SELECTIONS START" || echo "0")
SUBMIT_COUNT=$(adb logcat -d | grep -c "SUBMITTING.*STYLES" || echo "0")

echo "Key Flow Events:"
echo "- Initialization started: $INIT_COUNT"
echo "- Load alternatives started: $LOAD_COUNT"
echo "- Service responses: $SERVICE_COUNT"
echo "- UI updates started: $UPDATE_COUNT"
echo "- Style submissions: $SUBMIT_COUNT"
echo ""

# Determine flow status
if [ $INIT_COUNT -gt 0 ] && [ $LOAD_COUNT -gt 0 ] && [ $SERVICE_COUNT -gt 0 ] && [ $UPDATE_COUNT -gt 0 ] && [ $SUBMIT_COUNT -gt 0 ]; then
    echo -e "${GREEN}[SUCCESS]${NC} Data flow appears to be working correctly!"
else
    echo -e "${RED}[ISSUE]${NC} Data flow has problems. Check the individual steps above."
fi

echo ""
echo "Full emoji-related logs saved to: emoji_data_flow_test_$(date +%Y%m%d_%H%M%S).txt"
adb logcat -d | grep -E "(Emoji|Customize|Battery|Carousel)" > emoji_data_flow_test_$(date +%Y%m%d_%H%M%S).txt

echo "=========================================="
echo "Test completed at: $(date)"
echo "==========================================" 