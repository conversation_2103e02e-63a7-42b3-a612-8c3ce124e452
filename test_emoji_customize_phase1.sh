#!/bin/bash

# =============================================================================
# Emoji Customize Activity Phase 1 Test Script
# =============================================================================
# This script validates the Phase 1 implementation of the emoji customize 
# activity using ADB commands and logcat monitoring.
#
# Requirements:
# - Device connected via ADB
# - App installed with package name: com.fc.p.tj.charginganimation.batterycharging.chargeeffect
# - Debug builds with logging enabled
#
# Usage: ./test_emoji_customize_phase1.sh
# =============================================================================

set -e

# Configuration
PACKAGE_NAME="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
EMOJI_CUSTOMIZE_ACTIVITY="com.tqhit.battery.one.features.emoji.presentation.customize.EmojiCustomizeActivity"
LOG_FILE="emoji_customize_phase1_test_$(date +%Y%m%d_%H%M%S).log"
LOGCAT_TAGS="EmojiCustomizeActivity|BatteryComponentAdapter|EmojiComponentAdapter|CustomizeViewModel|EMOJI_CUSTOMIZE"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if device is connected
check_device() {
    log "Checking ADB device connection..."
    if ! adb devices | grep -q "device$"; then
        log_error "No Android device connected via ADB"
        exit 1
    fi
    log_success "Device connected"
}

# Check if app is installed
check_app_installed() {
    log "Checking if app is installed..."
    if ! adb shell pm list packages | grep -q "$PACKAGE_NAME"; then
        log_error "App not installed: $PACKAGE_NAME"
        exit 1
    fi
    log_success "App is installed"
}

# Clear app data and logs
setup_test_environment() {
    log "Setting up test environment..."
    
    # Clear logcat
    adb logcat -c
    
    # Clear app data (optional - comment out if you want to keep existing data)
    # adb shell pm clear "$PACKAGE_NAME"
    
    log_success "Test environment ready"
}

# Start logcat monitoring in background
start_logcat_monitoring() {
    log "Starting logcat monitoring for tags: $LOGCAT_TAGS"
    
    # Start logcat in background and save PID
    adb logcat -s "$LOGCAT_TAGS" > "${LOG_FILE%.log}_logcat.log" &
    LOGCAT_PID=$!
    
    log "Logcat monitoring started (PID: $LOGCAT_PID)"
    sleep 2
}

# Stop logcat monitoring
stop_logcat_monitoring() {
    if [ ! -z "$LOGCAT_PID" ]; then
        log "Stopping logcat monitoring (PID: $LOGCAT_PID)"
        kill $LOGCAT_PID 2>/dev/null || true
    fi
}

# Launch the app and navigate to emoji feature
launch_app() {
    log "Launching app..."
    
    # Launch main activity
    adb shell am start -n "$PACKAGE_NAME/.activity.main.MainActivity" -a android.intent.action.MAIN -c android.intent.category.LAUNCHER
    
    sleep 3
    log_success "App launched"
}

# Navigate to emoji battery gallery
navigate_to_emoji_gallery() {
    log "Navigating to emoji battery gallery..."
    
    # This depends on your app's navigation structure
    # You may need to adjust these coordinates or use different methods
    
    # Tap on emoji battery feature (adjust coordinates as needed)
    # adb shell input tap 500 1000
    
    # Or use activity manager to start directly
    adb shell am start -n "$PACKAGE_NAME/com.tqhit.battery.one.activity.main.MainActivity" \
        --es "fragment" "emoji_battery" 2>/dev/null || true
    
    sleep 2
    log_success "Navigated to emoji gallery"
}

# Test Phase 1 functionality
test_phase1_functionality() {
    log "Testing Phase 1 functionality..."
    
    # Test 1: Select a style from gallery to open customize activity
    log "Test 1: Opening customize activity..."
    
    # Simulate tapping on first item in gallery (adjust coordinates)
    adb shell input tap 200 600
    sleep 3
    
    # Check if customize activity is launched
    current_activity=$(adb shell dumpsys activity activities | grep "mFocusedActivity" | head -1)
    if echo "$current_activity" | grep -q "EmojiCustomizeActivity"; then
        log_success "EmojiCustomizeActivity launched successfully"
    else
        log_warning "EmojiCustomizeActivity may not be launched. Current: $current_activity"
    fi
    
    # Test 2: Check if component carousels are populated
    log "Test 2: Checking component carousels..."
    sleep 2
    
    # Test battery component carousel (swipe horizontally)
    log "Testing battery component carousel..."
    adb shell input swipe 300 800 600 800 500  # Swipe right on battery carousel
    sleep 1
    adb shell input swipe 600 800 300 800 500  # Swipe left
    sleep 1
    
    # Test emoji component carousel 
    log "Testing emoji component carousel..."
    adb shell input swipe 300 1000 600 1000 500  # Swipe right on emoji carousel
    sleep 1
    adb shell input swipe 600 1000 300 1000 500  # Swipe left
    sleep 1
    
    # Test 3: Test component selection
    log "Test 3: Testing component selection..."
    
    # Tap on different battery components
    adb shell input tap 400 800  # Tap on battery carousel
    sleep 1
    adb shell input tap 500 800  # Tap on another battery component
    sleep 1
    
    # Tap on different emoji components
    adb shell input tap 400 1000  # Tap on emoji carousel
    sleep 1
    adb shell input tap 500 1000  # Tap on another emoji component
    sleep 1
    
    # Test 4: Test UI controls
    log "Test 4: Testing UI controls..."
    
    # Test toggles (adjust coordinates based on your layout)
    adb shell input tap 400 1200  # Toggle emoji visibility
    sleep 1
    adb shell input tap 400 1250  # Toggle percentage visibility
    sleep 1
    
    # Test sliders (swipe to change values)
    adb shell input swipe 300 1300 500 1300 300  # Font size slider
    sleep 1
    adb shell input swipe 300 1350 500 1350 300  # Emoji scale slider
    sleep 1
    
    log_success "Phase 1 functionality tests completed"
}

# Validate logs for expected behavior
validate_logs() {
    log "Validating logs for expected behavior..."
    
    local logcat_file="${LOG_FILE%.log}_logcat.log"
    
    # Check for key log messages that indicate Phase 1 is working
    local checks=(
        "EMOJI_CUSTOMIZE_VIEWMODEL.*Loading style alternatives"
        "EMOJI_CUSTOMIZE_ADAPTER.*Creating new.*ViewHolder"
        "EMOJI_CUSTOMIZE_ADAPTER.*Binding.*component at position"
        "EMOJI_CUSTOMIZE_ACTIVITY.*Setting up RecyclerViews"
        "EMOJI_CUSTOMIZE_ACTIVITY.*Submitting.*components to adapter"
    )
    
    local passed=0
    local total=${#checks[@]}
    
    for check in "${checks[@]}"; do
        if grep -qE "$check" "$logcat_file" 2>/dev/null; then
            log_success "✓ Found: $check"
            ((passed++))
        else
            log_warning "✗ Missing: $check"
        fi
    done
    
    log "Validation Results: $passed/$total checks passed"
    
    if [ $passed -eq $total ]; then
        log_success "All Phase 1 validations passed!"
        return 0
    else
        log_warning "Some Phase 1 validations failed. Check logs for details."
        return 1
    fi
}

# Cleanup function
cleanup() {
    log "Cleaning up..."
    stop_logcat_monitoring
    
    # Return to home screen
    adb shell input keyevent KEYCODE_HOME
    
    log "Test completed. Logs saved to:"
    log "  - Main log: $LOG_FILE"
    log "  - Logcat: ${LOG_FILE%.log}_logcat.log"
}

# Main test execution
main() {
    log "=== Emoji Customize Activity Phase 1 Test Script ==="
    log "Package: $PACKAGE_NAME"
    log "Activity: $EMOJI_CUSTOMIZE_ACTIVITY"
    log "Log file: $LOG_FILE"
    log ""
    
    # Setup
    check_device
    check_app_installed
    setup_test_environment
    
    # Start monitoring
    start_logcat_monitoring
    
    # Execute tests
    launch_app
    navigate_to_emoji_gallery
    test_phase1_functionality
    
    # Wait for logs to be written
    sleep 5
    
    # Validation
    stop_logcat_monitoring
    if validate_logs; then
        log_success "🎉 Phase 1 implementation validation PASSED!"
        exit 0
    else
        log_error "❌ Phase 1 implementation validation FAILED!"
        exit 1
    fi
}

# Trap cleanup on script exit
trap cleanup EXIT

# Run main function
main "$@" 