07-03 11:45:45.524 24157 24157 D BatteryApplication: STARTUP_TIMING: BatteryApplication.onCreate() started at 1751517945524
07-03 11:45:46.023 24157 24157 D BatteryApplication: STARTUP_TIMING: onCreateExt() started at 1751517946023
07-03 11:45:46.198 24157 24157 D BatteryApplication: STARTUP_TIMING: Theme initialization took 174ms
07-03 11:45:46.203 24157 24157 D BatteryApplication: STARTUP_TIMING: Language initialization took 4ms
07-03 11:45:46.205 24157 24157 D BatteryApplication: STARTUP_TIMING: onCreateExt() completed in 182ms
07-03 11:45:46.225 24157 24157 D BatteryApplication: TIMING: super.onCreate() took 700ms
07-03 11:45:46.236 24157 24157 D BatteryApplication: TIMING: Preferences operations took 1ms
07-03 11:45:46.245 24157 24157 D BatteryApplication: STARTUP_TIMING: BatteryApplication.onCreate() completed in 721ms
07-03 11:45:46.247 24157 24201 D BatteryApplication: STARTUP_TIMING: Async initialization started
07-03 11:45:46.248 24157 24201 D BatteryApplication: STARTUP_TIMING: Firebase init started at 1751517946247 on thread: DefaultDispatcher-worker-2 (ID: 65)
07-03 11:45:46.251 24157 24201 D BatteryApplication: STARTUP_TIMING: Firebase init completed in 4ms on thread: DefaultDispatcher-worker-2
07-03 11:45:46.251 24157 24201 D BatteryApplication: STARTUP_TIMING: Memory usage after Firebase init: 16MB
07-03 11:45:46.251 24157 24201 D BatteryApplication: DEPRECATED: BatteryStatusService startup disabled - using CoreBatteryStatsService instead
07-03 11:45:46.251 24157 24201 D BatteryApplication: Starting CoreBatteryStatsService from Application
07-03 11:45:46.374 24157 24201 W BatteryApplication: Missing required permissions for foreground service, starting in fallback mode
07-03 11:45:46.375 24157 24201 D CoreBatteryServiceHelper: Starting CoreBatteryStatsService
07-03 11:45:46.375 24157 24201 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:46.375 24157 24201 W CoreBatteryServiceHelper: Cannot start foreground service due to missing requirements
07-03 11:45:46.393 24157 24201 D CoreBatteryServiceHelper: CoreBatteryStatsService started as regular service (fallback mode)
07-03 11:45:46.436 24157 24202 D BatteryApplication: STARTUP_TIMING: Async remote config initialization took 227ms
07-03 11:45:46.499 24157 24201 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:46.500 24157 24201 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:46.515 24157 24201 D BatteryApplication: CoreBatteryStatsService startup status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:45:46.516 24157 24201 D BatteryApplication: Starting ChargingOverlayService from Application
07-03 11:45:46.546 24157 24201 D BatteryApplication: STARTUP_TIMING: Async battery services startup took 295ms
07-03 11:45:46.576 24157 24201 D BatteryApplication: STARTUP_TIMING: Animation preloading disabled for performance optimization
07-03 11:45:46.576 24157 24201 D BatteryApplication: STARTUP_TIMING: Thumbnail preloading disabled for performance optimization
07-03 11:45:46.576 24157 24201 D BatteryApplication: STARTUP_TIMING: Starting parallel MAX SDK initialization
07-03 11:45:46.591 24157 24201 D BatteryApplication: STARTUP_TIMING: Total async initialization took 344ms
07-03 11:45:46.598 24157 24202 D BatteryApplication: MAX_INIT: Starting AppLovin MAX SDK initialization at 1751517946598 on thread: DefaultDispatcher-worker-3 (ID: 66)
07-03 11:45:46.599 24157 24202 D BatteryApplication: MAX_INIT: Memory usage before MAX SDK init: 17MB
07-03 11:45:47.182 24157 24202 D BatteryApplication: MAX_INIT: SDK settings configured in 583ms
07-03 11:45:47.291 24157 24202 D BatteryApplication: MAX_INIT: Initialization configuration created in 109ms
07-03 11:45:47.297 24157 24202 D BatteryApplication: MAX_INIT: Starting SDK initialization call at 1751517947297
07-03 11:45:47.524 24157 24200 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:47.524 24157 24200 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:47.524 24157 24200 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:45:47.625 24157 24201 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:47.625 24157 24201 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:47.625 24157 24201 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:45:47.727 24157 24200 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:47.727 24157 24200 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:47.727 24157 24200 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:45:47.828 24157 24201 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:47.828 24157 24201 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:47.828 24157 24201 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:45:47.928 24157 24200 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:47.929 24157 24200 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:47.929 24157 24200 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:45:48.064 24157 24201 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:48.066 24157 24201 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:48.071 24157 24201 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:45:48.173 24157 24201 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:48.173 24157 24201 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:48.173 24157 24201 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:45:48.274 24157 24201 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:48.274 24157 24201 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:48.274 24157 24201 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:45:48.374 24157 24201 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:48.375 24157 24201 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:48.375 24157 24201 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:45:48.476 24157 24201 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:48.476 24157 24201 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:48.476 24157 24201 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:45:48.584 24157 24201 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:48.584 24157 24201 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:48.584 24157 24201 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:45:48.685 24157 24201 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:48.685 24157 24201 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:48.686 24157 24201 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:45:48.779 24157 24157 D BatteryApplication: App moved to foreground
07-03 11:45:48.780 24157 24157 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:48.786 24157 24201 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:48.786 24157 24201 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:48.786 24157 24201 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
07-03 11:45:48.820 24157 24157 D CoreBatteryStatsService: STARTUP_TIMING: CoreBatteryStatsService.onCreate() started at 1751517948820 on thread: main (ID: 2)
07-03 11:45:48.821 24157 24157 D CoreBatteryStatsService: CoreBatteryStatsService created
07-03 11:45:48.822 24157 24157 D CoreBatteryStatsService: STARTUP_TIMING: BatteryManager initialization took 1ms
07-03 11:45:48.857 24157 24157 D CoreBatteryStatsService: Unified notification channel created: unified_battery_monitor_channel
07-03 11:45:48.857 24157 24157 D CoreBatteryStatsService: STARTUP_TIMING: Notification channel creation took 35ms
07-03 11:45:48.857 24157 24157 D CoreBatteryStatsService: STARTUP_TIMING: CoreBatteryStatsService.onCreate() completed in 37ms
07-03 11:45:48.857 24157 24157 D CoreBatteryStatsService: STARTUP_TIMING: onStartCommand called at 1751517948857 on thread: main (ID: 2) with action: com.tqhit.battery.one.ACTION_START_CORE_BATTERY_MONITORING
07-03 11:45:48.857 24157 24157 D CoreBatteryStatsService: STARTUP_TIMING: Starting core battery monitoring at 1751517948857
07-03 11:45:48.857 24157 24157 D CoreBatteryStatsService: Attempting to start foreground service (Android 35, attempt 1)
07-03 11:45:48.858 24157 24157 W CoreBatteryStatsService: POST_NOTIFICATIONS permission not granted, cannot start foreground service
07-03 11:45:48.858 24157 24157 W CoreBatteryStatsService: Foreground service start failed, continuing in fallback mode
07-03 11:45:48.858 24157 24157 D CoreBatteryStatsService: Starting battery status monitoring
07-03 11:45:48.864 24157 24157 D CoreBatteryStatsService: Battery event receiver registered
07-03 11:45:48.864 24157 24157 D CoreBatteryStatsService: Getting initial battery status
07-03 11:45:48.867 24157 24157 V CoreBatteryStatsService: Extracting battery status from intent
07-03 11:45:48.871 24157 24157 D CoreBatteryStatsService: Extracted battery status: level=67/100 (67%), status=4 (charging=false), plugged=0, voltage=5000mV, temp=25.0°C, current=900000µA
07-03 11:45:48.872 24157 24157 D CoreBatteryStatsProvider: CORE_BATTERY_PROVIDER: Initial status set: CoreBatteryStatus(percentage=67, isCharging=false, pluggedSource=0, currentMicroAmperes=900000, voltageMillivolts=5000, temperatureCelsius=25.0, timestampEpochMillis=1751517948871)
07-03 11:45:48.872 24157 24157 D CoreBatteryStatus: CORE_BATTERY_STATUS_CREATED: ID=458593926, Percentage=67%, Charging=false, PluggedSource=UNPLUGGED, Current=900000µA, Voltage=5000mV, Temperature=25.0°C, Timestamp=1751517948871
07-03 11:45:48.872 24157 24157 V CoreBatteryStatsService: Skipping notification update - not running as foreground service
07-03 11:45:48.872 24157 24157 D CoreBatteryStatsService: Initial battery status emitted: CoreBatteryStatus(percentage=67, isCharging=false, pluggedSource=0, currentMicroAmperes=900000, voltageMillivolts=5000, temperatureCelsius=25.0, timestampEpochMillis=1751517948871)
07-03 11:45:48.890 24157 24200 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:48.891 24157 24200 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:48.892 24157 24200 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:45:48.994 24157 24200 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:48.994 24157 24200 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:48.994 24157 24200 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:45:49.095 24157 24201 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:49.095 24157 24201 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:49.095 24157 24201 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:45:49.103 24157 24157 V CoreBatteryStatsService: Battery status changed - processing intent
07-03 11:45:49.103 24157 24157 V CoreBatteryStatsService: Extracting battery status from intent
07-03 11:45:49.109 24157 24157 D CoreBatteryStatsService: Extracted battery status: level=67/100 (67%), status=4 (charging=false), plugged=0, voltage=5000mV, temp=25.0°C, current=900000µA
07-03 11:45:49.109 24157 24157 D CoreBatteryStatsService: BATTERY_UPDATE: === NEW BATTERY STATUS DETECTED ===
07-03 11:45:49.109 24157 24157 D CoreBatteryStatsService: BATTERY_UPDATE: Status details:
07-03 11:45:49.109 24157 24157 D CoreBatteryStatsService: BATTERY_UPDATE:   Percentage: 67%
07-03 11:45:49.109 24157 24157 D CoreBatteryStatsService: BATTERY_UPDATE:   Charging: false
07-03 11:45:49.109 24157 24157 D CoreBatteryStatsService: BATTERY_UPDATE:   Current: 900000µA
07-03 11:45:49.109 24157 24157 D CoreBatteryStatsService: BATTERY_UPDATE:   Temperature: 25.0°C
07-03 11:45:49.117 24157 24157 D CoreBatteryStatsService: BATTERY_UPDATE:   Timestamp: 11:45:49
07-03 11:45:49.127 24157 24157 V CoreBatteryStatsProvider: CORE_BATTERY_PROVIDER: Status updated with no significant changes
07-03 11:45:49.128 24157 24157 D CoreBatteryStatus: CORE_BATTERY_STATUS_CREATED: ID=458594648, Percentage=67%, Charging=false, PluggedSource=UNPLUGGED, Current=900000µA, Voltage=5000mV, Temperature=25.0°C, Timestamp=1751517949109
07-03 11:45:49.128 24157 24157 V CoreBatteryStatsService: Skipping notification update - not running as foreground service
07-03 11:45:49.128 24157 24157 D CoreBatteryStatsService: BATTERY_UPDATE: ✅ Status updated and emitted to all observers
07-03 11:45:49.128 24157 24157 D CoreBatteryStatsService: BATTERY_UPDATE: This will trigger health recalculation in HealthRepository
07-03 11:45:49.128 24157 24157 D CoreBatteryStatsService: Battery status updated and emitted: CoreBatteryStatus(percentage=67, isCharging=false, pluggedSource=0, currentMicroAmperes=900000, voltageMillivolts=5000, temperatureCelsius=25.0, timestampEpochMillis=1751517949109)
07-03 11:45:49.206 24157 24200 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:49.209 24157 24200 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:49.211 24157 24200 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:45:49.356 24157 24200 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:49.357 24157 24200 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:49.357 24157 24200 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:45:49.500 24157 24200 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:49.500 24157 24200 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:49.500 24157 24200 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:45:49.604 24157 24202 D CoreBatteryServiceHelper: CoreBatteryStatsService running status: true
07-03 11:45:49.605 24157 24202 W CoreBatteryServiceHelper: POST_NOTIFICATIONS permission not granted
07-03 11:45:49.606 24157 24202 D ServiceInitHelper: CoreBatteryStatsService status: {isServiceRunning=true, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, isForegroundServiceActive=false, isRunningInFallbackMode=true, foregroundStartupAttempts=0, isReceiverRegistered=true}
07-03 11:45:49.745 24157 24201 W ServiceInitHelper: STARTUP_TIMING: CoreBatteryStatsService not ready within timeout
07-03 11:45:49.745 24157 24201 D ServiceInitHelper: STARTUP_TIMING: Battery services initialization completed
07-03 11:45:49.846 24157 24223 D InitProgressManager: SPLASH_PROGRESS: Progress updated - 25% - Battery services ready
07-03 11:45:49.847 24157 24223 D InitProgressManager: SPLASH_PROGRESS: Battery services initialization completed successfully
07-03 11:45:49.847 24157 24223 D BatteryApplication: STARTUP_TIMING: Starting deferred MAX SDK initialization from UI
07-03 11:45:49.848 24157 24223 D BatteryApplication: MAX_INIT: Starting AppLovin MAX SDK initialization at 1751517949848 on thread: DefaultDispatcher-worker-6 (ID: 77)
07-03 11:45:49.848 24157 24223 D BatteryApplication: MAX_INIT: Memory usage before MAX SDK init: 46MB
07-03 11:45:49.848 24157 24223 D BatteryApplication: MAX_INIT: SDK settings configured in 0ms
07-03 11:45:49.848 24157 24223 D BatteryApplication: MAX_INIT: Initialization configuration created in 0ms
07-03 11:45:49.849 24157 24223 D BatteryApplication: MAX_INIT: Starting SDK initialization call at 1751517949849
